# Vidur Accuracy Validation Pipeline

这个验证流程用于复现和验证 Vidur 论文中的准确性声明：
> "We validate the fidelity of Vidur on several LLMs and show that it estimates inference latency with less than 9% error across the range."

## 文件结构

```
├── step1_run_vllm_benchmark.py    # 步骤1: 运行 vLLM 基准测试
├── step2_extract_data.py          # 步骤2: 从 vLLM 结果中提取数据
├── step3_run_vidur_simulation.py  # 步骤3: 运行 Vidur 仿真
├── step4_compare_results.py       # 步骤4: 对比分析结果
├── run_full_validation.py         # 主控制脚本（一键运行）
└── README_validation.md           # 使用说明（本文件）
```

## 快速开始

### 方法1: 一键运行完整流程

```bash
# 使用默认配置运行完整验证
python run_full_validation.py

# 使用自定义配置
python run_full_validation.py --model meta-llama/Meta-Llama-3-8B --dataset sharegpt --num-prompts 512
```

### 方法2: 分步骤运行

```bash
# 步骤1: 运行 vLLM 基准测试
python step1_run_vllm_benchmark.py --dataset sharegpt --num-prompts 512

# 步骤2: 提取数据
python step2_extract_data.py

# 步骤3: 运行 Vidur 仿真
python step3_run_vidur_simulation.py

# 步骤4: 对比结果
python step4_compare_results.py
```

## 详细说明

### 步骤1: vLLM 基准测试

**功能**: 启动 vLLM 服务器并运行基准测试，收集真实的性能数据

**输入**: 
- 模型名称 (默认: meta-llama/Meta-Llama-3-8B)
- 数据集 (sharegpt/sonnet/random/burstgpt)
- 请求数量 (默认: 512)
- 请求率 (默认: 6.45 QPS)

**输出**: 
- `vllm_benchmark_results/vllm_results_*.json` - vLLM 基准测试结果

**使用示例**:
```bash
python step1_run_vllm_benchmark.py --dataset sharegpt --num-prompts 256 --request-rate 5.0
```

### 步骤2: 数据提取

**功能**: 从 vLLM 结果中提取 token 数量和时间信息，转换为 Vidur 输入格式

**输入**: vLLM 基准测试结果 JSON 文件

**输出**: 
- `vidur_input_data/vidur_trace_*.csv` - Vidur 仿真输入文件
- `vidur_input_data/vllm_metrics_*.csv` - vLLM 性能指标文件

**使用示例**:
```bash
python step2_extract_data.py --input vllm_benchmark_results/vllm_results_sharegpt_20250805_120000.json
```

### 步骤3: Vidur 仿真

**功能**: 使用提取的 trace 数据运行 Vidur 仿真

**输入**: Vidur trace CSV 文件

**输出**: 
- `simulator_output/*/request_metrics.csv` - Vidur 仿真结果

**使用示例**:
```bash
python step3_run_vidur_simulation.py --trace-file vidur_input_data/vidur_trace_sharegpt_20250805_120000.csv
```

### 步骤4: 结果对比

**功能**: 对比 vLLM 和 Vidur 的性能指标，计算误差并生成报告

**输入**: 
- vLLM 性能指标 CSV 文件
- Vidur 仿真结果目录

**输出**: 
- `comparison_results/comparison_report.txt` - 详细分析报告
- `comparison_results/performance_comparison.png` - 可视化图表
- `comparison_results/detailed_comparison.csv` - 详细对比数据

## 配置选项

### 支持的模型
- meta-llama/Meta-Llama-3-8B (默认)
- meta-llama/Meta-Llama-3-70B
- 其他 vLLM 支持的模型

### 支持的数据集
- **sharegpt**: 真实对话数据 (推荐)
- **sonnet**: 诗歌生成任务
- **random**: 合成随机数据
- **burstgpt**: 突发请求模式

### 关键参数
- `--num-prompts`: 测试请求数量 (默认: 512)
- `--request-rate`: 请求率 QPS (默认: 6.45)
- `--model`: 模型名称
- `--dataset`: 数据集类型

## 结果解读

### 验证标准
论文声明: "inference latency with less than 9% error"

### 关键指标
1. **TTFT (Time to First Token)**: 首个 token 生成时间
2. **TPOT (Time per Output Token)**: 每个输出 token 的平均时间
3. **E2E Latency**: 端到端延迟

### 成功标准
- 平均端到端延迟误差 < 9%
- 大部分请求的误差在合理范围内
- 误差分布符合预期

## 故障排除

### 常见问题

1. **vLLM 服务器启动失败**
   - 检查 GPU 内存是否足够
   - 确认模型是否已下载
   - 检查端口是否被占用

2. **Vidur 仿真失败**
   - 确认 trace 文件格式正确
   - 检查 Vidur 安装是否完整
   - 验证配置参数是否合理

3. **数据对比异常**
   - 检查数据文件是否存在
   - 确认数据长度是否匹配
   - 验证指标计算是否正确

### 调试选项

```bash
# 检查依赖
python run_full_validation.py --check-deps

# 跳过 vLLM 基准测试（使用现有结果）
python run_full_validation.py --skip-vllm

# 跳过 Vidur 仿真（使用现有结果）
python run_full_validation.py --skip-vidur

# 快速测试（少量请求）
python run_full_validation.py --num-prompts 50
```

## 依赖要求

### Python 包
```bash
pip install pandas numpy matplotlib seaborn requests
```

### 系统要求
- Python 3.8+
- CUDA GPU (用于 vLLM)
- 足够的 GPU 内存运行目标模型
- Vidur 已正确安装

## 预期运行时间

- **vLLM 基准测试**: 10-30 分钟 (取决于模型大小和请求数量)
- **数据提取**: < 1 分钟
- **Vidur 仿真**: 5-15 分钟
- **结果对比**: < 1 分钟

**总计**: 约 20-50 分钟

## 输出文件说明

### 目录结构
```
├── vllm_benchmark_results/     # vLLM 基准测试结果
├── vidur_input_data/          # 提取的数据文件
├── simulator_output/          # Vidur 仿真输出
└── comparison_results/        # 对比分析结果
```

### 关键文件
- `comparison_report.txt`: 包含验证结论的详细报告
- `performance_comparison.png`: 性能对比可视化图表
- `detailed_comparison.csv`: 每个请求的详细对比数据
