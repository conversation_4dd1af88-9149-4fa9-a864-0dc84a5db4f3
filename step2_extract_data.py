#!/usr/bin/env python3
"""
Step 2: Extract Data from vLLM Results
This script extracts token counts and timing information from vLLM benchmark results
and converts them to Vidur input format.
"""

import json
import pandas as pd
import argparse
import glob
from pathlib import Path
import numpy as np

# Colors for output
RED = '\033[0;31m'
GREEN = '\033[0;32m'
YELLOW = '\033[1;33m'
BLUE = '\033[0;34m'
NC = '\033[0m'

def print_info(msg):
    print(f"{GREEN}[INFO]{NC} {msg}")

def print_warning(msg):
    print(f"{YELLOW}[WARNING]{NC} {msg}")

def print_error(msg):
    print(f"{RED}[ERROR]{NC} {msg}")

def print_step(msg):
    print(f"{BLUE}[STEP]{NC} {msg}")

class VLLMDataExtractor:
    def __init__(self, vllm_results_file):
        self.vllm_results_file = Path(vllm_results_file)
        self.output_dir = Path("vidur_input_data")
        self.output_dir.mkdir(exist_ok=True)
        
    def load_vllm_results(self):
        """加载 vLLM 基准测试结果"""
        print_step("Loading vLLM benchmark results...")
        
        try:
            with open(self.vllm_results_file, 'r') as f:
                data = json.load(f)
            
            print_info(f"Loaded results from: {self.vllm_results_file}")
            
            # 检查数据结构
            if 'results' in data:
                results = data['results']
            elif isinstance(data, list):
                results = data
            else:
                # 尝试直接使用数据
                results = data
            
            print_info(f"Found {len(results)} requests in results")
            return data, results
            
        except Exception as e:
            print_error(f"Failed to load vLLM results: {e}")
            raise
    
    def extract_vidur_trace(self, vllm_data, results):
        """从 vLLM 结果中提取 Vidur 需要的 trace 格式"""
        print_step("Extracting trace data for Vidur...")
        
        vidur_trace = []
        
        # 获取基准时间（第一个请求的时间）
        base_time = None
        if results and 'timestamp' in results[0]:
            base_time = results[0]['timestamp']
        
        for i, result in enumerate(results):
            try:
                # 提取到达时间
                if 'timestamp' in result and base_time is not None:
                    arrived_at = result['timestamp'] - base_time
                elif 'request_time' in result:
                    if base_time is None:
                        base_time = result['request_time']
                    arrived_at = result['request_time'] - base_time
                else:
                    # 如果没有时间戳，根据请求率估算
                    request_rate = vllm_data.get('request_rate', 6.45)
                    arrived_at = i / request_rate
                
                # 提取 token 数量
                num_prefill_tokens = result.get('prompt_len', 
                                               result.get('input_len', 
                                                         result.get('prefill_tokens', 0)))
                
                num_decode_tokens = result.get('output_len',
                                              result.get('decode_tokens',
                                                        result.get('generated_tokens', 0)))
                
                # 验证数据有效性
                if num_prefill_tokens <= 0 or num_decode_tokens <= 0:
                    print_warning(f"Invalid token counts for request {i}: "
                                f"prefill={num_prefill_tokens}, decode={num_decode_tokens}")
                    continue
                
                vidur_trace.append({
                    'arrived_at': float(arrived_at),
                    'num_prefill_tokens': int(num_prefill_tokens),
                    'num_decode_tokens': int(num_decode_tokens)
                })
                
            except Exception as e:
                print_warning(f"Failed to process request {i}: {e}")
                continue
        
        print_info(f"Successfully extracted {len(vidur_trace)} valid requests")
        return vidur_trace
    
    def save_vidur_trace(self, trace_data, output_filename=None):
        """保存 Vidur trace 文件"""
        if output_filename is None:
            timestamp = self.vllm_results_file.stem.replace('vllm_results_', '')
            output_filename = f"vidur_trace_{timestamp}.csv"
        
        output_path = self.output_dir / output_filename
        
        print_step(f"Saving Vidur trace to: {output_path}")
        
        df = pd.DataFrame(trace_data)
        
        # 确保列的顺序正确
        df = df[['arrived_at', 'num_prefill_tokens', 'num_decode_tokens']]
        
        # 按到达时间排序
        df = df.sort_values('arrived_at').reset_index(drop=True)
        
        # 保存到 CSV
        df.to_csv(output_path, index=False)
        
        print_info(f"Vidur trace saved successfully!")
        print_info(f"File: {output_path}")
        
        # 打印统计信息
        self._print_trace_stats(df)
        
        return str(output_path)
    
    def _print_trace_stats(self, df):
        """打印 trace 统计信息"""
        print_info("Trace Statistics:")
        print(f"  Total requests: {len(df)}")
        print(f"  Time span: {df['arrived_at'].min():.2f}s - {df['arrived_at'].max():.2f}s")
        print(f"  Duration: {df['arrived_at'].max() - df['arrived_at'].min():.2f}s")
        
        print(f"  Prefill tokens - Min: {df['num_prefill_tokens'].min()}, "
              f"Max: {df['num_prefill_tokens'].max()}, "
              f"Mean: {df['num_prefill_tokens'].mean():.1f}")
        
        print(f"  Decode tokens - Min: {df['num_decode_tokens'].min()}, "
              f"Max: {df['num_decode_tokens'].max()}, "
              f"Mean: {df['num_decode_tokens'].mean():.1f}")
        
        # 计算实际 QPS
        duration = df['arrived_at'].max() - df['arrived_at'].min()
        if duration > 0:
            actual_qps = len(df) / duration
            print(f"  Actual QPS: {actual_qps:.2f}")
    
    def save_vllm_metrics(self, vllm_data, results, output_filename=None):
        """保存 vLLM 的性能指标用于后续对比"""
        if output_filename is None:
            timestamp = self.vllm_results_file.stem.replace('vllm_results_', '')
            output_filename = f"vllm_metrics_{timestamp}.csv"
        
        output_path = self.output_dir / output_filename
        
        print_step(f"Saving vLLM metrics to: {output_path}")
        
        metrics_data = []
        
        for i, result in enumerate(results):
            try:
                metrics = {
                    'request_id': i,
                    'prompt_len': result.get('prompt_len', result.get('input_len', 0)),
                    'output_len': result.get('output_len', result.get('generated_tokens', 0)),
                    'ttft': result.get('ttft', result.get('time_to_first_token', 0)),
                    'tpot': result.get('tpot', result.get('time_per_output_token', 0)),
                    'e2e_latency': result.get('latency', result.get('e2e_latency', 0)),
                    'throughput': result.get('throughput', 0)
                }
                
                # 计算一些派生指标
                if metrics['output_len'] > 0 and metrics['e2e_latency'] > 0:
                    if metrics['tpot'] == 0:  # 如果没有 TPOT，尝试计算
                        decode_time = metrics['e2e_latency'] - metrics['ttft']
                        if decode_time > 0 and metrics['output_len'] > 1:
                            metrics['tpot'] = decode_time / (metrics['output_len'] - 1)
                
                metrics_data.append(metrics)
                
            except Exception as e:
                print_warning(f"Failed to extract metrics for request {i}: {e}")
                continue
        
        df = pd.DataFrame(metrics_data)
        df.to_csv(output_path, index=False)
        
        print_info(f"vLLM metrics saved successfully!")
        print_info(f"File: {output_path}")
        
        return str(output_path)

def find_latest_vllm_results():
    """查找最新的 vLLM 结果文件"""
    results_dir = Path("vllm_benchmark_results")
    if not results_dir.exists():
        return None
    
    json_files = list(results_dir.glob("vllm_results_*.json"))
    if not json_files:
        return None
    
    # 返回最新的文件
    return max(json_files, key=lambda x: x.stat().st_mtime)

def main():
    parser = argparse.ArgumentParser(description="Extract data from vLLM results for Vidur")
    parser.add_argument("--input", "-i", 
                       help="vLLM results JSON file (if not specified, will use latest)")
    parser.add_argument("--output-trace", 
                       help="Output trace CSV filename")
    parser.add_argument("--output-metrics",
                       help="Output metrics CSV filename")
    
    args = parser.parse_args()
    
    # 确定输入文件
    if args.input:
        input_file = Path(args.input)
        if not input_file.exists():
            print_error(f"Input file not found: {input_file}")
            return 1
    else:
        input_file = find_latest_vllm_results()
        if input_file is None:
            print_error("No vLLM results found. Please run step1_run_vllm_benchmark.py first.")
            return 1
        print_info(f"Using latest vLLM results: {input_file}")
    
    try:
        # 创建提取器
        extractor = VLLMDataExtractor(input_file)
        
        # 加载 vLLM 结果
        vllm_data, results = extractor.load_vllm_results()
        
        # 提取 Vidur trace
        trace_data = extractor.extract_vidur_trace(vllm_data, results)
        
        if not trace_data:
            print_error("No valid trace data extracted!")
            return 1
        
        # 保存文件
        trace_file = extractor.save_vidur_trace(trace_data, args.output_trace)
        metrics_file = extractor.save_vllm_metrics(vllm_data, results, args.output_metrics)
        
        print_info("Data extraction completed successfully!")
        print_info(f"Vidur trace file: {trace_file}")
        print_info(f"vLLM metrics file: {metrics_file}")
        print_info("Next step: Run 'python step3_run_vidur_simulation.py' to run Vidur simulation")
        
        return 0
        
    except Exception as e:
        print_error(f"Data extraction failed: {e}")
        return 1

if __name__ == "__main__":
    exit(main())
