#!/usr/bin/env python3
"""
Trace Extractor
Extracts trace data from vLLM benchmark results for Vidur simulation
"""

import json
import pandas as pd
import sys
from pathlib import Path
from typing import Dict, Any, List

from utils.config_loader import get_config


class TraceExtractor:
    """Extract trace data from vLLM results for Vidur input"""
    
    def __init__(self):
        self.config = get_config()
        self.output_config = self.config.get_output_config()
    
    def extract_trace_from_vllm_results(self, vllm_results_path: str = None) -> pd.DataFrame:
        """Extract trace data from vLLM benchmark results"""
        
        if vllm_results_path is None:
            vllm_results_path = Path(self.output_config['vllm_results_dir']) / self.output_config['vllm_results_file']
        
        print(f"Loading vLLM results from: {vllm_results_path}")
        
        # Load vLLM results
        try:
            with open(vllm_results_path, 'r') as f:
                vllm_data = json.load(f)
        except FileNotFoundError:
            raise FileNotFoundError(f"vLLM results file not found: {vllm_results_path}")
        except json.JSONDecodeError as e:
            raise ValueError(f"Failed to parse vLLM results JSON: {e}")
        
        # Extract individual request data
        trace_data = []
        
        if 'requests' in vllm_data:
            requests_data = vllm_data['requests']
        elif 'results' in vllm_data:
            requests_data = vllm_data['results']
        else:
            # Try to find request data in the structure
            print("Available keys in vLLM results:", list(vllm_data.keys()))
            raise ValueError("Could not find request data in vLLM results. Expected 'requests' or 'results' key.")
        
        print(f"Found {len(requests_data)} requests in vLLM results")
        
        # Process each request
        for i, request in enumerate(requests_data):
            try:
                # Extract timing information
                # Different vLLM versions may have different field names
                timestamp = request.get('timestamp', i * (1.0 / self.config.get('benchmark.request_rate', 6.45)))
                
                # Extract token counts
                prompt_len = request.get('prompt_len', request.get('input_len', request.get('num_prefill_tokens', 0)))
                output_len = request.get('output_len', request.get('generated_len', request.get('num_decode_tokens', 0)))
                
                if prompt_len == 0 or output_len == 0:
                    print(f"Warning: Request {i} has zero tokens (prompt: {prompt_len}, output: {output_len})")
                
                trace_entry = {
                    'arrived_at': timestamp,
                    'num_prefill_tokens': prompt_len,
                    'num_decode_tokens': output_len
                }
                
                trace_data.append(trace_entry)
                
            except Exception as e:
                print(f"Warning: Failed to process request {i}: {e}")
                print(f"Request data: {request}")
                continue
        
        if not trace_data:
            raise ValueError("No valid trace data extracted from vLLM results")
        
        # Create DataFrame
        trace_df = pd.DataFrame(trace_data)
        
        # Sort by arrival time
        trace_df = trace_df.sort_values('arrived_at').reset_index(drop=True)
        
        # Normalize arrival times to start from 0
        if len(trace_df) > 0:
            min_time = trace_df['arrived_at'].min()
            trace_df['arrived_at'] = trace_df['arrived_at'] - min_time
        
        print(f"✓ Extracted {len(trace_df)} trace entries")
        return trace_df
    
    def save_trace(self, trace_df: pd.DataFrame, output_path: str = None) -> str:
        """Save trace data to CSV file"""
        
        if output_path is None:
            output_path = Path(self.output_config['vllm_results_dir']) / self.output_config['extracted_trace_file']
        
        # Ensure output directory exists
        Path(output_path).parent.mkdir(parents=True, exist_ok=True)
        
        # Save to CSV
        trace_df.to_csv(output_path, index=False)
        
        print(f"✓ Trace data saved to: {output_path}")
        return str(output_path)
    
    def print_trace_summary(self, trace_df: pd.DataFrame):
        """Print summary of extracted trace data"""
        print("\n" + "="*60)
        print("EXTRACTED TRACE SUMMARY")
        print("="*60)
        
        print(f"Total Requests: {len(trace_df)}")
        print(f"Time Range: {trace_df['arrived_at'].min():.2f}s - {trace_df['arrived_at'].max():.2f}s")
        print(f"Total Duration: {trace_df['arrived_at'].max() - trace_df['arrived_at'].min():.2f}s")
        
        print(f"\nPrefill Tokens:")
        print(f"  Mean: {trace_df['num_prefill_tokens'].mean():.1f}")
        print(f"  Min: {trace_df['num_prefill_tokens'].min()}")
        print(f"  Max: {trace_df['num_prefill_tokens'].max()}")
        print(f"  P50: {trace_df['num_prefill_tokens'].quantile(0.5):.1f}")
        print(f"  P95: {trace_df['num_prefill_tokens'].quantile(0.95):.1f}")
        
        print(f"\nDecode Tokens:")
        print(f"  Mean: {trace_df['num_decode_tokens'].mean():.1f}")
        print(f"  Min: {trace_df['num_decode_tokens'].min()}")
        print(f"  Max: {trace_df['num_decode_tokens'].max()}")
        print(f"  P50: {trace_df['num_decode_tokens'].quantile(0.5):.1f}")
        print(f"  P95: {trace_df['num_decode_tokens'].quantile(0.95):.1f}")
        
        # Calculate estimated QPS
        if len(trace_df) > 1:
            duration = trace_df['arrived_at'].max() - trace_df['arrived_at'].min()
            qps = (len(trace_df) - 1) / duration if duration > 0 else 0
            print(f"\nEstimated QPS: {qps:.2f}")
        
        print("="*60)
    
    def validate_trace(self, trace_df: pd.DataFrame) -> bool:
        """Validate extracted trace data"""
        issues = []
        
        # Check for required columns
        required_cols = ['arrived_at', 'num_prefill_tokens', 'num_decode_tokens']
        for col in required_cols:
            if col not in trace_df.columns:
                issues.append(f"Missing required column: {col}")
        
        # Check for negative values
        if (trace_df['arrived_at'] < 0).any():
            issues.append("Negative arrival times found")
        
        if (trace_df['num_prefill_tokens'] <= 0).any():
            issues.append("Zero or negative prefill tokens found")
        
        if (trace_df['num_decode_tokens'] <= 0).any():
            issues.append("Zero or negative decode tokens found")
        
        # Check for reasonable values
        if trace_df['num_prefill_tokens'].max() > 50000:
            issues.append("Unusually large prefill token counts (>50k)")
        
        if trace_df['num_decode_tokens'].max() > 10000:
            issues.append("Unusually large decode token counts (>10k)")
        
        if issues:
            print("⚠️  Trace validation issues:")
            for issue in issues:
                print(f"  - {issue}")
            return False
        else:
            print("✓ Trace validation passed")
            return True


def main():
    """Main function"""
    print("Starting Trace Extraction...")
    
    try:
        extractor = TraceExtractor()
        
        # Extract trace from vLLM results
        trace_df = extractor.extract_trace_from_vllm_results()
        
        # Validate trace
        extractor.validate_trace(trace_df)
        
        # Print summary
        extractor.print_trace_summary(trace_df)
        
        # Save trace
        output_path = extractor.save_trace(trace_df)
        
        print(f"\n✓ Trace extraction completed successfully!")
        print(f"Trace file ready for Vidur: {output_path}")
        
    except Exception as e:
        print(f"✗ Trace extraction failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
