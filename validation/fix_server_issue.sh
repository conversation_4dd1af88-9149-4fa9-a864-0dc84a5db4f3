#!/bin/bash

# Fix vLLM Server Issue Script
# This script addresses the max_model_len configuration problem

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

echo "vLLM Server Issue Fix"
echo "===================="
echo ""

print_step "1. Stopping any existing vLLM server processes..."

# Kill any existing vLLM processes
pkill -f "vllm.entrypoints.openai.api_server" || true
sleep 2

# Kill processes using port 8000
if lsof -Pi :8000 -sTCP:LISTEN -t >/dev/null 2>&1; then
    print_info "Killing process using port 8000..."
    PID=$(lsof -Pi :8000 -sTCP:LISTEN -t)
    kill -9 $PID 2>/dev/null || true
    sleep 1
fi

print_step "2. Cleaning up PID file..."
rm -f vllm_server.pid

print_step "3. Explaining the issue..."
print_info "The error occurred because:"
print_info "  - Model's max_position_embeddings: 8192"
print_info "  - Config specified max_model_len: 16384"
print_info "  - vLLM doesn't allow exceeding the model's native limit"

print_step "4. Applied fixes:"
print_info "  ✓ Set VLLM_ALLOW_LONG_MAX_MODEL_LEN=1 environment variable"
print_info "  ✓ Reduced max_model_len to 8192 in config.yaml"

print_step "5. Verifying configuration..."
python3 -c "
from utils.config_loader import get_config
config = get_config()
max_len = config.get('model.max_model_len')
print(f'Current max_model_len: {max_len}')
if max_len <= 8192:
    print('✓ Configuration is now compatible')
else:
    print('⚠️  Consider reducing max_model_len to 8192 or less')
"

print_step "6. Ready to restart server"
print_info "You can now run: ./scripts/start_vllm_server.sh"
print_info ""
print_info "Alternative: If you need longer sequences, you can:"
print_info "  export VLLM_ALLOW_LONG_MAX_MODEL_LEN=1"
print_info "  # Then edit config.yaml to set max_model_len back to 16384"
print_info "  # But be aware this may cause CUDA errors or incorrect outputs"

echo ""
print_info "Issue fixed! Server should start successfully now."
