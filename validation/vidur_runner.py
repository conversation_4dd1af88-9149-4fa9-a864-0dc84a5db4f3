#!/usr/bin/env python3
"""
Vidur Runner
Runs Vidur simulation with extracted trace data
"""

import subprocess
import sys
import shutil
from pathlib import Path
from typing import Dict, Any

from utils.config_loader import get_config


class VidurRunner:
    """Run Vidur simulation with extracted trace data"""
    
    def __init__(self):
        self.config = get_config()
        self.config.create_output_dirs()
        
        # Get configurations
        self.model_config = self.config.get_model_config()
        self.hardware_config = self.config.get_hardware_config()
        self.vidur_scheduler_config = self.config.get_vidur_scheduler_config()
        self.predictor_config = self.config.get_predictor_config()
        self.output_config = self.config.get_output_config()
        self.benchmark_config = self.config.get_benchmark_config()
    
    def check_trace_file(self, trace_file_path: str) -> bool:
        """Check if trace file exists and is valid"""
        trace_path = Path(trace_file_path)
        
        if not trace_path.exists():
            print(f"✗ Trace file not found: {trace_file_path}")
            return False
        
        # Check if file has content
        if trace_path.stat().st_size == 0:
            print(f"✗ Trace file is empty: {trace_file_path}")
            return False
        
        print(f"✓ Trace file found: {trace_file_path}")
        return True
    
    def build_vidur_command(self, trace_file_path: str) -> list:
        """Build Vidur command with all parameters"""
        
        cmd = [
            "python", "-m", "vidur.main",
            
            # Model configuration
            "--replica_config_model_name", self.model_config['name'],
            
            # Hardware configuration
            "--replica_config_device", self.hardware_config['device'],
            "--cluster_config_num_replicas", str(self.hardware_config['num_replicas']),
            "--replica_config_tensor_parallel_size", str(self.model_config['tensor_parallel_size']),
            "--replica_config_num_pipeline_stages", str(self.hardware_config['num_pipeline_stages']),
            
            # Request generator configuration
            "--request_generator_config_type", "synthetic",
            "--synthetic_request_generator_config_num_requests", str(self.benchmark_config['num_prompts']),
            
            # Length generator configuration (using trace)
            "--length_generator_config_type", "trace",
            "--trace_request_length_generator_config_max_tokens", str(self.model_config['max_model_len']),
            "--trace_request_length_generator_config_trace_file", trace_file_path,
            
            # Interval generator configuration (using trace)
            "--interval_generator_config_type", "trace",
            "--trace_request_interval_generator_config_trace_file", trace_file_path,
            
            # Scheduler configuration
            "--replica_scheduler_config_type", self.vidur_scheduler_config['type'],
            "--sarathi_scheduler_config_batch_size_cap", str(self.vidur_scheduler_config['batch_size_cap']),
            "--sarathi_scheduler_config_chunk_size", str(self.vidur_scheduler_config['chunk_size']),
            
            # Predictor configuration
            "--random_forrest_execution_time_predictor_config_prediction_max_prefill_chunk_size", 
            str(self.predictor_config['max_prefill_chunk_size']),
            "--random_forrest_execution_time_predictor_config_prediction_max_batch_size", 
            str(self.predictor_config['max_batch_size']),
            "--random_forrest_execution_time_predictor_config_prediction_max_tokens_per_request", 
            str(self.predictor_config['max_tokens_per_request'])
        ]
        
        return cmd
    
    def run_vidur_simulation(self, trace_file_path: str = None) -> str:
        """Run Vidur simulation"""
        
        # Use default trace file if not provided
        if trace_file_path is None:
            trace_file_path = Path(self.output_config['vllm_results_dir']) / self.output_config['extracted_trace_file']
        
        # Check trace file
        if not self.check_trace_file(trace_file_path):
            raise FileNotFoundError(f"Trace file not found or invalid: {trace_file_path}")
        
        # Build command
        cmd = self.build_vidur_command(str(trace_file_path))
        
        print("Running Vidur simulation...")
        print(f"Command: {' '.join(cmd)}")
        print("This may take several minutes...")
        
        # Run Vidur simulation
        try:
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                check=True,
                timeout=1800  # 30 minutes timeout
            )
            
            print("✓ Vidur simulation completed successfully!")
            
            if result.stdout:
                print("STDOUT (last 1000 chars):", result.stdout[-1000:])
            
            if result.stderr:
                print("STDERR (last 1000 chars):", result.stderr[-1000:])
            
        except subprocess.TimeoutExpired:
            raise RuntimeError("Vidur simulation timed out after 30 minutes")
        except subprocess.CalledProcessError as e:
            print(f"✗ Vidur simulation failed with return code {e.returncode}")
            if e.stdout:
                print("STDOUT:", e.stdout[-1000:])
            if e.stderr:
                print("STDERR:", e.stderr[-1000:])
            raise RuntimeError(f"Vidur simulation failed: {e}")
        
        # Find and move results
        return self.organize_vidur_results()
    
    def organize_vidur_results(self) -> str:
        """Find and organize Vidur simulation results"""
        
        # Vidur typically creates results in simulator_output directory
        simulator_output_dir = Path("simulator_output")
        
        if not simulator_output_dir.exists():
            raise FileNotFoundError("Vidur simulator_output directory not found")
        
        # Find the most recent output directory
        output_dirs = [d for d in simulator_output_dir.iterdir() if d.is_dir()]
        if not output_dirs:
            raise FileNotFoundError("No output directories found in simulator_output")
        
        # Get the most recent directory (by name, which includes timestamp)
        latest_output_dir = max(output_dirs, key=lambda x: x.name)
        
        print(f"Found Vidur results in: {latest_output_dir}")
        
        # Copy results to our organized output directory
        target_dir = Path(self.output_config['vidur_results_dir'])
        target_dir.mkdir(parents=True, exist_ok=True)
        
        # Copy all files from latest output to target directory
        for item in latest_output_dir.iterdir():
            if item.is_file():
                target_file = target_dir / item.name
                shutil.copy2(item, target_file)
                print(f"Copied: {item.name}")
        
        print(f"✓ Vidur results organized in: {target_dir}")
        return str(target_dir)
    
    def print_vidur_summary(self, results_dir: str):
        """Print summary of Vidur simulation results"""
        results_path = Path(results_dir)
        
        print("\n" + "="*60)
        print("VIDUR SIMULATION RESULTS SUMMARY")
        print("="*60)
        
        # Check for key result files
        key_files = [
            "request_metrics.csv",
            "config.json",
            "plots/request_e2e_time.csv"
        ]
        
        print("Result files:")
        for file_name in key_files:
            file_path = results_path / file_name
            if file_path.exists():
                print(f"  ✓ {file_name}")
            else:
                print(f"  ✗ {file_name} (missing)")
        
        # Try to read basic metrics from request_metrics.csv
        request_metrics_file = results_path / "request_metrics.csv"
        if request_metrics_file.exists():
            try:
                import pandas as pd
                df = pd.read_csv(request_metrics_file)
                
                print(f"\nSimulation Metrics:")
                print(f"  Total Requests: {len(df)}")
                
                if 'request_e2e_time' in df.columns:
                    print(f"  Mean E2E Latency: {df['request_e2e_time'].mean():.3f}s")
                    print(f"  P95 E2E Latency: {df['request_e2e_time'].quantile(0.95):.3f}s")
                
                if 'prefill_e2e_time' in df.columns:
                    print(f"  Mean TTFT: {df['prefill_e2e_time'].mean():.3f}s")
                    print(f"  P95 TTFT: {df['prefill_e2e_time'].quantile(0.95):.3f}s")
                
            except Exception as e:
                print(f"  Could not read metrics: {e}")
        
        print("="*60)


def main():
    """Main function"""
    print("Starting Vidur Simulation...")
    
    try:
        runner = VidurRunner()
        results_dir = runner.run_vidur_simulation()
        runner.print_vidur_summary(results_dir)
        
        print(f"\n✓ Vidur simulation completed successfully!")
        print(f"Results saved to: {results_dir}")
        
    except Exception as e:
        print(f"✗ Vidur simulation failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
