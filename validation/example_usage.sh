#!/bin/bash

# Example Usage Script for Vidur Validation
# This script demonstrates how to run the validation step by step

set -e

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

echo "Vidur Validation - Example Usage"
echo "================================"
echo ""

print_warning "This is an example script. Please run commands manually for better control."
echo ""

print_step "1. Setup (run once)"
echo "   ./setup.sh"
echo ""

print_step "2. Update configuration"
echo "   # Edit config.yaml with your model path"
echo "   # Example: model.local_path: '/share_data/llm_weights/Meta-Llama-3-8B'"
echo ""

print_step "3. Start vLLM server (in terminal 1)"
echo "   cd validation"
echo "   ./scripts/start_vllm_server.sh"
echo ""

print_step "4. Run validation (in terminal 2)"
echo "   # Can run from anywhere in the Vidur repository:"
echo "   python validation/run_full_validation.py"
echo "   # or:"
echo "   cd validation && python run_full_validation.py"
echo ""

print_step "5. Alternative: Run step by step"
echo "   python vllm_benchmark.py      # Run vLLM benchmark"
echo "   python trace_extractor.py     # Extract trace data"
echo "   python vidur_runner.py        # Run Vidur simulation"
echo "   python comparison_analyzer.py # Compare results"
echo ""

print_step "6. Stop server when done"
echo "   ./scripts/stop_vllm_server.sh"
echo ""

print_info "Expected runtime: 15-30 minutes total"
print_info "Results will be saved in results/ directory"
echo ""

print_warning "Make sure you're running from within the Vidur repository!"
print_warning "The script will automatically find the Vidur root directory."
print_info "You can run from the repository root or the validation/ subdirectory."
