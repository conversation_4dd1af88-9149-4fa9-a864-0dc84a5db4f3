#!/usr/bin/env python3
"""
Full Validation Pipeline
Runs the complete Vidur validation process
"""

import sys
import time
from pathlib import Path

# Add current directory to path for imports
sys.path.append(str(Path(__file__).parent))

from utils.config_loader import get_config
from vllm_benchmark import V<PERSON>MBenchmarkRunner
from trace_extractor import TraceExtractor
from vidur_runner import VidurRunner
from comparison_analyzer import ComparisonAnalyzer


class FullValidationPipeline:
    """Complete Vidur validation pipeline"""
    
    def __init__(self):
        self.config = get_config()
        print("Vidur Validation Pipeline Initialized")
        print(f"Model: {self.config.get('model.name')}")
        print(f"Dataset: {self.config.get('benchmark.dataset_name')}")
        print(f"Requests: {self.config.get('benchmark.num_prompts')}")
        print(f"QPS: {self.config.get('benchmark.request_rate')}")
    
    def run_step(self, step_name: str, step_func, *args, **kwargs):
        """Run a validation step with error handling and timing"""
        print(f"\n{'='*60}")
        print(f"STEP: {step_name}")
        print(f"{'='*60}")
        
        start_time = time.time()
        
        try:
            result = step_func(*args, **kwargs)
            elapsed = time.time() - start_time
            print(f"✓ {step_name} completed successfully in {elapsed:.1f}s")
            return result
            
        except Exception as e:
            elapsed = time.time() - start_time
            print(f"✗ {step_name} failed after {elapsed:.1f}s: {e}")
            raise
    
    def run_full_validation(self):
        """Run the complete validation pipeline"""
        
        print("\n🚀 Starting Full Vidur Validation Pipeline")
        print("This process will take approximately 15-30 minutes")
        
        pipeline_start = time.time()
        
        try:
            # Step 1: Run vLLM Benchmark
            vllm_runner = VLLMBenchmarkRunner()
            vllm_results = self.run_step(
                "1. vLLM Benchmark", 
                vllm_runner.run_benchmark
            )
            
            # Step 2: Extract Trace Data
            trace_extractor = TraceExtractor()
            trace_df = self.run_step(
                "2. Trace Extraction",
                trace_extractor.extract_trace_from_vllm_results
            )
            
            # Validate and save trace
            trace_extractor.validate_trace(trace_df)
            trace_file = self.run_step(
                "2b. Save Trace File",
                trace_extractor.save_trace,
                trace_df
            )
            
            # Step 3: Run Vidur Simulation
            vidur_runner = VidurRunner()
            vidur_results_dir = self.run_step(
                "3. Vidur Simulation",
                vidur_runner.run_vidur_simulation,
                trace_file
            )
            
            # Step 4: Compare Results
            analyzer = ComparisonAnalyzer()
            
            # Load and analyze results
            vllm_results_loaded = self.run_step(
                "4a. Load vLLM Results",
                analyzer.load_vllm_results
            )
            
            vidur_df = self.run_step(
                "4b. Load Vidur Results", 
                analyzer.load_vidur_results,
                vidur_results_dir
            )
            
            vllm_df = self.run_step(
                "4c. Extract vLLM Metrics",
                analyzer.extract_vllm_metrics,
                vllm_results_loaded
            )
            
            errors = self.run_step(
                "4d. Calculate Errors",
                analyzer.calculate_errors,
                vllm_df, vidur_df
            )
            
            self.run_step(
                "4e. Create Plots",
                analyzer.create_comparison_plots,
                vllm_df, vidur_df
            )
            
            self.run_step(
                "4f. Generate Report",
                analyzer.generate_report,
                errors, vllm_df, vidur_df
            )
            
            # Pipeline completed successfully
            total_time = time.time() - pipeline_start
            
            print(f"\n🎉 VALIDATION PIPELINE COMPLETED SUCCESSFULLY!")
            print(f"Total time: {total_time/60:.1f} minutes")
            
            # Print final summary
            self.print_final_summary(errors)
            
            return True
            
        except Exception as e:
            total_time = time.time() - pipeline_start
            print(f"\n💥 VALIDATION PIPELINE FAILED!")
            print(f"Error: {e}")
            print(f"Time elapsed: {total_time/60:.1f} minutes")
            return False
    
    def print_final_summary(self, errors):
        """Print final validation summary"""
        
        print(f"\n{'='*60}")
        print("FINAL VALIDATION RESULTS")
        print(f"{'='*60}")
        
        threshold = self.config.get('validation.error_threshold', 9.0)
        
        print(f"Paper Claim: Vidur estimates inference latency with less than {threshold}% error")
        print(f"Our Results:")
        
        for metric, error_stats in errors.items():
            error_pct = error_stats['mean_relative_error']
            status = "✅ VALIDATED" if error_pct <= threshold else "❌ NOT VALIDATED"
            print(f"  {metric}: {error_pct:.2f}% error - {status}")
        
        passed_metrics = sum(1 for error_stats in errors.values() 
                            if error_stats['mean_relative_error'] <= threshold)
        total_metrics = len(errors)
        
        print(f"\nOverall Validation: {passed_metrics}/{total_metrics} metrics passed")
        
        if passed_metrics == total_metrics:
            print("🏆 CONCLUSION: Paper claim VALIDATED!")
            print("   Vidur successfully estimates inference latency within the claimed accuracy.")
        else:
            print("⚠️  CONCLUSION: Paper claim NOT FULLY VALIDATED")
            print("   Some metrics exceed the claimed error threshold.")
        
        print(f"{'='*60}")
        
        # Print next steps
        print(f"\nNext Steps:")
        print(f"  1. Check detailed results in: {self.config.get('output.comparison_results_dir')}")
        print(f"  2. Review comparison plots and report")
        print(f"  3. Consider running with different datasets or configurations")


def find_vidur_root():
    """Find Vidur repository root directory"""
    current_dir = Path.cwd()

    # Check current directory first
    if (current_dir / "vidur").exists():
        return current_dir

    # Check parent directories
    for parent in current_dir.parents:
        if (parent / "vidur").exists():
            return parent

    return None

def main():
    """Main function"""

    # Find Vidur repository root
    vidur_root = find_vidur_root()

    if vidur_root is None:
        print("❌ Error: Could not find Vidur repository root directory")
        print("   Please ensure you're running from within the Vidur repository")
        print("   The 'vidur' directory should be present in the repository root")
        sys.exit(1)

    # Change to Vidur root directory if needed
    original_cwd = Path.cwd()
    if vidur_root != original_cwd:
        print(f"📁 Changing to Vidur root directory: {vidur_root}")
        import os
        os.chdir(vidur_root)
    
    # Check if vLLM server is running
    config = get_config()
    server_config = config.get_server_config()

    print("🔍 Pre-flight checks:")
    print(f"  ✓ Vidur directory found: {vidur_root}")
    print(f"  ✓ Configuration loaded")
    print(f"  ✓ Working directory: {Path.cwd()}")
    print(f"  ⚠️  Make sure vLLM server is running on {server_config['base_url']}")

    # Provide context-aware instructions
    validation_dir = vidur_root / "validation"
    if validation_dir.exists():
        print(f"     Run: cd {validation_dir} && ./scripts/start_vllm_server.sh")
    else:
        print(f"     Run: ./validation/scripts/start_vllm_server.sh")
    
    # Ask for confirmation
    response = input("\nReady to start validation? (y/N): ").strip().lower()
    if response != 'y':
        print("Validation cancelled.")
        sys.exit(0)
    
    # Run validation
    pipeline = FullValidationPipeline()
    success = pipeline.run_full_validation()
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
