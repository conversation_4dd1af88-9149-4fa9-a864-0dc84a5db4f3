#!/usr/bin/env python3
"""
Comparison Analyzer
Compares vLLM and Vidur results to validate Vidur's accuracy
"""

import json
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import sys
from pathlib import Path
from typing import Dict, Any, List, Tuple

from utils.config_loader import get_config


class ComparisonAnalyzer:
    """Analyze and compare vLLM vs Vidur results"""
    
    def __init__(self):
        self.config = get_config()
        self.output_config = self.config.get_output_config()
        self.validation_config = self.config.get_validation_config()
        
        # Create comparison results directory
        self.comparison_dir = Path(self.output_config['comparison_results_dir'])
        self.comparison_dir.mkdir(parents=True, exist_ok=True)
    
    def load_vllm_results(self, vllm_results_path: str = None) -> Dict[str, Any]:
        """Load vLLM benchmark results"""
        if vllm_results_path is None:
            vllm_results_path = Path(self.output_config['vllm_results_dir']) / self.output_config['vllm_results_file']
        
        try:
            with open(vllm_results_path, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            raise FileNotFoundError(f"vLLM results file not found: {vllm_results_path}")
        except json.JSONDecodeError as e:
            raise ValueError(f"Failed to parse vLLM results JSON: {e}")
    
    def load_vidur_results(self, vidur_results_dir: str = None) -> pd.DataFrame:
        """Load Vidur simulation results"""
        if vidur_results_dir is None:
            vidur_results_dir = self.output_config['vidur_results_dir']
        
        request_metrics_file = Path(vidur_results_dir) / "request_metrics.csv"
        
        try:
            return pd.read_csv(request_metrics_file)
        except FileNotFoundError:
            raise FileNotFoundError(f"Vidur results file not found: {request_metrics_file}")
    
    def extract_vllm_metrics(self, vllm_results: Dict[str, Any]) -> pd.DataFrame:
        """Extract metrics from vLLM results into DataFrame"""
        
        # Try different possible structures
        if 'requests' in vllm_results:
            requests_data = vllm_results['requests']
        elif 'results' in vllm_results:
            requests_data = vllm_results['results']
        else:
            raise ValueError("Could not find request data in vLLM results")
        
        metrics = []
        for i, request in enumerate(requests_data):
            try:
                # Extract metrics (field names may vary by vLLM version)
                e2e_latency = request.get('e2e_latency', request.get('latency', 0))
                ttft = request.get('ttft', request.get('time_to_first_token', 0))
                tpot = request.get('tpot', request.get('time_per_output_token', 0))
                
                # Convert to seconds if in milliseconds
                if e2e_latency > 100:  # Likely in milliseconds
                    e2e_latency /= 1000
                if ttft > 100:
                    ttft /= 1000
                if tpot > 1:  # TPOT should be small
                    tpot /= 1000
                
                metrics.append({
                    'request_id': i,
                    'e2e_latency': e2e_latency,
                    'ttft': ttft,
                    'tpot': tpot,
                    'prompt_len': request.get('prompt_len', request.get('input_len', 0)),
                    'output_len': request.get('output_len', request.get('generated_len', 0))
                })
            except Exception as e:
                print(f"Warning: Failed to extract metrics from request {i}: {e}")
                continue
        
        return pd.DataFrame(metrics)
    
    def align_datasets(self, vllm_df: pd.DataFrame, vidur_df: pd.DataFrame) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """Align vLLM and Vidur datasets for comparison"""
        
        # Take the minimum number of requests
        min_requests = min(len(vllm_df), len(vidur_df))
        
        print(f"Aligning datasets: vLLM={len(vllm_df)}, Vidur={len(vidur_df)}, using {min_requests} requests")
        
        # Sort both by request order and take first N
        vllm_aligned = vllm_df.head(min_requests).reset_index(drop=True)
        vidur_aligned = vidur_df.head(min_requests).reset_index(drop=True)
        
        return vllm_aligned, vidur_aligned
    
    def calculate_errors(self, vllm_df: pd.DataFrame, vidur_df: pd.DataFrame) -> Dict[str, Dict[str, float]]:
        """Calculate error metrics between vLLM and Vidur results"""
        
        # Align datasets
        vllm_aligned, vidur_aligned = self.align_datasets(vllm_df, vidur_df)
        
        # Define metric mappings (vLLM column -> Vidur column)
        metric_mappings = {
            'e2e_latency': 'request_e2e_time',
            'ttft': 'prefill_e2e_time'
        }
        
        errors = {}
        
        for vllm_col, vidur_col in metric_mappings.items():
            if vllm_col in vllm_aligned.columns and vidur_col in vidur_aligned.columns:
                vllm_values = vllm_aligned[vllm_col].values
                vidur_values = vidur_aligned[vidur_col].values
                
                # Remove zero values to avoid division by zero
                mask = (vllm_values > 0) & (vidur_values > 0)
                if mask.sum() == 0:
                    print(f"Warning: No valid values for {vllm_col}")
                    continue
                
                vllm_clean = vllm_values[mask]
                vidur_clean = vidur_values[mask]
                
                # Calculate various error metrics
                absolute_errors = np.abs(vllm_clean - vidur_clean)
                relative_errors = absolute_errors / vllm_clean * 100
                
                errors[vllm_col] = {
                    'mean_absolute_error': np.mean(absolute_errors),
                    'mean_relative_error': np.mean(relative_errors),
                    'median_relative_error': np.median(relative_errors),
                    'p95_relative_error': np.percentile(relative_errors, 95),
                    'max_relative_error': np.max(relative_errors),
                    'rmse': np.sqrt(np.mean((vllm_clean - vidur_clean) ** 2)),
                    'correlation': np.corrcoef(vllm_clean, vidur_clean)[0, 1]
                }
        
        return errors
    
    def create_comparison_plots(self, vllm_df: pd.DataFrame, vidur_df: pd.DataFrame):
        """Create comparison plots"""
        
        vllm_aligned, vidur_aligned = self.align_datasets(vllm_df, vidur_df)
        
        # Set up the plotting style
        plt.style.use('default')
        sns.set_palette("husl")
        
        # Create figure with subplots
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('vLLM vs Vidur Performance Comparison', fontsize=16)
        
        # Plot 1: E2E Latency Scatter Plot
        if 'e2e_latency' in vllm_aligned.columns and 'request_e2e_time' in vidur_aligned.columns:
            ax = axes[0, 0]
            ax.scatter(vllm_aligned['e2e_latency'], vidur_aligned['request_e2e_time'], alpha=0.6)
            ax.plot([0, max(vllm_aligned['e2e_latency'].max(), vidur_aligned['request_e2e_time'].max())], 
                   [0, max(vllm_aligned['e2e_latency'].max(), vidur_aligned['request_e2e_time'].max())], 
                   'r--', label='Perfect Prediction')
            ax.set_xlabel('vLLM E2E Latency (s)')
            ax.set_ylabel('Vidur E2E Latency (s)')
            ax.set_title('End-to-End Latency Comparison')
            ax.legend()
            ax.grid(True, alpha=0.3)
        
        # Plot 2: TTFT Scatter Plot
        if 'ttft' in vllm_aligned.columns and 'prefill_e2e_time' in vidur_aligned.columns:
            ax = axes[0, 1]
            ax.scatter(vllm_aligned['ttft'], vidur_aligned['prefill_e2e_time'], alpha=0.6)
            ax.plot([0, max(vllm_aligned['ttft'].max(), vidur_aligned['prefill_e2e_time'].max())], 
                   [0, max(vllm_aligned['ttft'].max(), vidur_aligned['prefill_e2e_time'].max())], 
                   'r--', label='Perfect Prediction')
            ax.set_xlabel('vLLM TTFT (s)')
            ax.set_ylabel('Vidur TTFT (s)')
            ax.set_title('Time to First Token Comparison')
            ax.legend()
            ax.grid(True, alpha=0.3)
        
        # Plot 3: Error Distribution
        if 'e2e_latency' in vllm_aligned.columns and 'request_e2e_time' in vidur_aligned.columns:
            ax = axes[1, 0]
            relative_errors = np.abs(vllm_aligned['e2e_latency'] - vidur_aligned['request_e2e_time']) / vllm_aligned['e2e_latency'] * 100
            ax.hist(relative_errors, bins=30, alpha=0.7, edgecolor='black')
            ax.axvline(relative_errors.mean(), color='red', linestyle='--', label=f'Mean: {relative_errors.mean():.1f}%')
            ax.axvline(9, color='orange', linestyle='--', label='9% Threshold')
            ax.set_xlabel('Relative Error (%)')
            ax.set_ylabel('Frequency')
            ax.set_title('E2E Latency Error Distribution')
            ax.legend()
            ax.grid(True, alpha=0.3)
        
        # Plot 4: Throughput Comparison (if available)
        ax = axes[1, 1]
        ax.text(0.5, 0.5, 'Throughput comparison\n(if available)', 
                ha='center', va='center', transform=ax.transAxes, fontsize=12)
        ax.set_title('Throughput Comparison')
        
        plt.tight_layout()
        
        # Save plot
        plot_path = self.comparison_dir / "comparison_plots.png"
        plt.savefig(plot_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"✓ Comparison plots saved to: {plot_path}")
    
    def generate_report(self, errors: Dict[str, Dict[str, float]], vllm_df: pd.DataFrame, vidur_df: pd.DataFrame):
        """Generate comparison report"""
        
        report_path = self.comparison_dir / "comparison_report.txt"
        
        with open(report_path, 'w') as f:
            f.write("VIDUR VALIDATION REPORT\n")
            f.write("=" * 50 + "\n\n")
            
            f.write(f"Dataset Size:\n")
            f.write(f"  vLLM Requests: {len(vllm_df)}\n")
            f.write(f"  Vidur Requests: {len(vidur_df)}\n\n")
            
            f.write("ERROR ANALYSIS:\n")
            f.write("-" * 20 + "\n")
            
            threshold = self.validation_config.get('error_threshold', 9.0)
            
            for metric, error_stats in errors.items():
                f.write(f"\n{metric.upper()}:\n")
                f.write(f"  Mean Relative Error: {error_stats['mean_relative_error']:.2f}%\n")
                f.write(f"  Median Relative Error: {error_stats['median_relative_error']:.2f}%\n")
                f.write(f"  P95 Relative Error: {error_stats['p95_relative_error']:.2f}%\n")
                f.write(f"  Max Relative Error: {error_stats['max_relative_error']:.2f}%\n")
                f.write(f"  RMSE: {error_stats['rmse']:.4f}\n")
                f.write(f"  Correlation: {error_stats['correlation']:.4f}\n")
                
                # Check if within threshold
                if error_stats['mean_relative_error'] <= threshold:
                    f.write(f"  ✓ PASS: Mean error ({error_stats['mean_relative_error']:.2f}%) <= {threshold}%\n")
                else:
                    f.write(f"  ✗ FAIL: Mean error ({error_stats['mean_relative_error']:.2f}%) > {threshold}%\n")
            
            f.write(f"\nVALIDATION SUMMARY:\n")
            f.write("-" * 20 + "\n")
            
            passed_metrics = sum(1 for error_stats in errors.values() 
                                if error_stats['mean_relative_error'] <= threshold)
            total_metrics = len(errors)
            
            f.write(f"Metrics Passed: {passed_metrics}/{total_metrics}\n")
            f.write(f"Error Threshold: {threshold}%\n")
            
            if passed_metrics == total_metrics:
                f.write("✓ OVERALL RESULT: PASS - Vidur accuracy validated!\n")
            else:
                f.write("✗ OVERALL RESULT: FAIL - Vidur accuracy needs improvement\n")
        
        print(f"✓ Comparison report saved to: {report_path}")
        
        # Also print summary to console
        self.print_summary(errors)
    
    def print_summary(self, errors: Dict[str, Dict[str, float]]):
        """Print comparison summary to console"""
        
        print("\n" + "="*60)
        print("VIDUR VALIDATION SUMMARY")
        print("="*60)
        
        threshold = self.validation_config.get('error_threshold', 9.0)
        
        for metric, error_stats in errors.items():
            status = "✓ PASS" if error_stats['mean_relative_error'] <= threshold else "✗ FAIL"
            print(f"{metric.upper()}: {error_stats['mean_relative_error']:.2f}% error - {status}")
        
        passed_metrics = sum(1 for error_stats in errors.values() 
                            if error_stats['mean_relative_error'] <= threshold)
        total_metrics = len(errors)
        
        print(f"\nOverall: {passed_metrics}/{total_metrics} metrics passed")
        
        if passed_metrics == total_metrics:
            print("🎉 VALIDATION SUCCESSFUL: Vidur accuracy confirmed!")
        else:
            print("⚠️  VALIDATION FAILED: Vidur needs accuracy improvements")
        
        print("="*60)


def main():
    """Main function"""
    print("Starting Comparison Analysis...")
    
    try:
        analyzer = ComparisonAnalyzer()
        
        # Load results
        print("Loading vLLM results...")
        vllm_results = analyzer.load_vllm_results()
        
        print("Loading Vidur results...")
        vidur_df = analyzer.load_vidur_results()
        
        # Extract vLLM metrics
        print("Extracting vLLM metrics...")
        vllm_df = analyzer.extract_vllm_metrics(vllm_results)
        
        # Calculate errors
        print("Calculating error metrics...")
        errors = analyzer.calculate_errors(vllm_df, vidur_df)
        
        # Create plots
        print("Creating comparison plots...")
        analyzer.create_comparison_plots(vllm_df, vidur_df)
        
        # Generate report
        print("Generating comparison report...")
        analyzer.generate_report(errors, vllm_df, vidur_df)
        
        print(f"\n✓ Comparison analysis completed!")
        print(f"Results saved to: {analyzer.comparison_dir}")
        
    except Exception as e:
        print(f"✗ Comparison analysis failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
