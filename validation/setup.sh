#!/bin/bash

# Vidur Validation Setup Script

set -e

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_info "Setting up Vidur Validation Framework..."

# Make scripts executable
print_info "Making scripts executable..."
chmod +x scripts/*.sh
chmod +x *.py

# Install Python dependencies
print_info "Installing Python dependencies..."
pip install pandas matplotlib seaborn pyyaml requests

# Create output directories
print_info "Creating output directories..."
mkdir -p results/vllm
mkdir -p results/vidur  
mkdir -p results/comparison

# Test configuration loading
print_info "Testing configuration..."
python3 -c "
from utils.config_loader import get_config
config = get_config()
print('✓ Configuration loaded successfully')
print(f'Model: {config.get(\"model.name\")}')
print(f'Local path: {config.get(\"model.local_path\")}')
print(f'Dataset: {config.get(\"benchmark.dataset_name\")}')
"

print_info "Setup completed successfully!"
print_info ""
print_info "Next steps:"
print_info "1. Update config.yaml with your model path"
print_info "2. Start vLLM server: ./scripts/start_vllm_server.sh"
print_info "3. Run validation: python run_full_validation.py"
