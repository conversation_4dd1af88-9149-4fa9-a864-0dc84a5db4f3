#!/bin/bash

# Check vLLM Server Status Script

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# Get the directory of this script
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# Load configuration to get port
CONFIG_VALUES=$(python3 -c "
import sys
sys.path.append('$PROJECT_DIR')
from utils.config_loader import get_config

config = get_config()
server_config = config.get_server_config()
print(f\"{server_config['host']}|{server_config['port']}\")
")

IFS='|' read -r HOST PORT <<< "$CONFIG_VALUES"

echo "vLLM Server Status Check"
echo "======================="
echo ""

print_step "1. Checking server process..."

# Check if vLLM process is running
VLLM_PIDS=$(pgrep -f "vllm.entrypoints.openai.api_server" || true)

if [ -z "$VLLM_PIDS" ]; then
    print_error "No vLLM server process found"
    echo ""
    print_info "To start the server:"
    print_info "  ./scripts/start_vllm_server.sh"
    exit 1
else
    print_info "vLLM server process(es) found: $VLLM_PIDS"
fi

print_step "2. Checking port usage..."

# Check if port is in use
if lsof -Pi :$PORT -sTCP:LISTEN -t >/dev/null 2>&1; then
    PID=$(lsof -Pi :$PORT -sTCP:LISTEN -t)
    print_info "Port $PORT is in use by PID: $PID"
else
    print_warning "Port $PORT is not in use (server may still be starting)"
fi

print_step "3. Checking server health..."

# Try to connect to health endpoint
if curl -s "http://$HOST:$PORT/health" >/dev/null 2>&1; then
    print_info "✓ Server is healthy and ready!"
    
    # Get server info
    echo ""
    print_step "4. Server information:"
    
    # Try to get models endpoint
    if curl -s "http://$HOST:$PORT/v1/models" >/dev/null 2>&1; then
        MODELS=$(curl -s "http://$HOST:$PORT/v1/models" | python3 -c "
import json, sys
try:
    data = json.load(sys.stdin)
    for model in data.get('data', []):
        print(f\"  Model: {model.get('id', 'Unknown')}\")
except:
    print('  Could not parse models info')
")
        echo "$MODELS"
    fi
    
    echo ""
    print_info "Server is ready for benchmarking!"
    print_info "You can now run: python run_full_validation.py"
    
else
    print_warning "Server is not ready yet (health check failed)"
    
    # Check recent logs if available
    echo ""
    print_step "4. Recent server activity:"
    
    # Show recent vLLM processes
    ps aux | grep vllm | grep -v grep | head -3
    
    echo ""
    print_info "Server may still be loading the model..."
    print_info "Large models can take 5-10 minutes to load"
    print_info ""
    print_info "You can:"
    print_info "  1. Wait a few more minutes and run this script again"
    print_info "  2. Check server logs manually"
    print_info "  3. Try: curl http://$HOST:$PORT/health"
fi

echo ""
print_step "5. Quick commands:"
echo "  Check status:  ./scripts/check_vllm_server.sh"
echo "  Stop server:   ./scripts/stop_vllm_server.sh"
echo "  Start server:  ./scripts/start_vllm_server.sh"
