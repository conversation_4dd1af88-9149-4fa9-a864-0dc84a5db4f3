#!/bin/bash

# Start vLLM Server Script
# This script starts the vLLM OpenAI API server with the configured parameters

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# Get the directory of this script
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# Load configuration (using Python to parse YAML)
print_step "Loading configuration..."

CONFIG_VALUES=$(python3 -c "
import sys
sys.path.append('$PROJECT_DIR')
from utils.config_loader import get_config

config = get_config()
model_config = config.get_model_config()
server_config = config.get_server_config()

print(f\"{model_config['local_path']}|{model_config['max_model_len']}|{model_config['tensor_parallel_size']}|{server_config['host']}|{server_config['port']}\")
")

IFS='|' read -r MODEL_PATH MAX_MODEL_LEN TENSOR_PARALLEL_SIZE HOST PORT <<< "$CONFIG_VALUES"

print_info "Configuration loaded:"
print_info "  Model Path: $MODEL_PATH"
print_info "  Max Model Length: $MAX_MODEL_LEN"
print_info "  Tensor Parallel Size: $TENSOR_PARALLEL_SIZE"
print_info "  Host: $HOST"
print_info "  Port: $PORT"

# Check if model path exists
if [ ! -d "$MODEL_PATH" ]; then
    print_error "Model path does not exist: $MODEL_PATH"
    print_error "Please check the model path in config.yaml"
    exit 1
fi

# Check if port is already in use
if lsof -Pi :$PORT -sTCP:LISTEN -t >/dev/null ; then
    print_warning "Port $PORT is already in use. Attempting to kill existing process..."
    PID=$(lsof -Pi :$PORT -sTCP:LISTEN -t)
    kill -9 $PID 2>/dev/null || true
    sleep 2
    
    if lsof -Pi :$PORT -sTCP:LISTEN -t >/dev/null ; then
        print_error "Failed to free port $PORT. Please manually kill the process and try again."
        exit 1
    fi
    print_info "Port $PORT is now available."
fi

# Check if Python environment is activated
if [ -z "$VIRTUAL_ENV" ] && [ -z "$CONDA_DEFAULT_ENV" ]; then
    print_warning "No virtual environment detected. Make sure you have activated your Python environment."
fi

print_step "Starting vLLM server..."

# # Set environment variable to allow longer max_model_len
# export VLLM_ALLOW_LONG_MAX_MODEL_LEN=1

# Start vLLM server
python -m vllm.entrypoints.openai.api_server \
    --model "$MODEL_PATH" \
    --host "$HOST" \
    --port "$PORT" \
    --tensor-parallel-size "$TENSOR_PARALLEL_SIZE" \
    --max-model-len "$MAX_MODEL_LEN" \
    --served-model-name "$(basename "$MODEL_PATH")" \
    --disable-log-requests \
    --trust-remote-code &

SERVER_PID=$!

print_info "vLLM server started with PID: $SERVER_PID"
print_info "Server URL: http://$HOST:$PORT"

# Save PID for later cleanup
echo $SERVER_PID > "$PROJECT_DIR/vllm_server.pid"

print_step "Waiting for server to be ready..."

# Wait for server to be ready
MAX_WAIT=300  # Increased to 5 minutes for large model loading
WAIT_TIME=0
SLEEP_INTERVAL=5  # Check every 5 seconds instead of 2

print_info "Model loading may take several minutes for large models..."
print_info "Progress indicators:"
print_info "  - Loading checkpoint shards"
print_info "  - Initializing CUDA kernels"
print_info "  - Setting up attention mechanisms"

while [ $WAIT_TIME -lt $MAX_WAIT ]; do
    # Check if health endpoint is available
    if curl -s "http://$HOST:$PORT/health" >/dev/null 2>&1; then
        print_info "Server is ready!"
        break
    fi

    # Check if server process is still running
    if ! kill -0 $SERVER_PID 2>/dev/null; then
        print_error "Server process died unexpectedly"
        exit 1
    fi

    # Show progress
    if [ $((WAIT_TIME % 30)) -eq 0 ] && [ $WAIT_TIME -gt 0 ]; then
        print_info "Still loading... (${WAIT_TIME}s elapsed, max ${MAX_WAIT}s)"
    fi

    sleep $SLEEP_INTERVAL
    WAIT_TIME=$((WAIT_TIME + SLEEP_INTERVAL))
    echo -n "."
done

if [ $WAIT_TIME -ge $MAX_WAIT ]; then
    print_error "Server failed to start within $MAX_WAIT seconds"
    print_warning "The server process may still be loading in the background"
    print_warning "Check with: ps aux | grep vllm"
    print_warning "Or try: curl http://$HOST:$PORT/health"
    exit 1
fi

print_info "vLLM server is running and ready to accept requests!"
print_info "To stop the server, run: ./scripts/stop_vllm_server.sh"
print_info "Or manually kill PID: $SERVER_PID"

# Keep the script running to show server logs
print_info "Server logs (Ctrl+C to stop):"
wait $SERVER_PID
