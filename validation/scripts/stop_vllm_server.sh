#!/bin/bash

# Stop vLLM Server Script

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${G<PERSON><PERSON>}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Get the directory of this script
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

PID_FILE="$PROJECT_DIR/vllm_server.pid"

# Load configuration to get port
CONFIG_VALUES=$(python3 -c "
import sys
sys.path.append('$PROJECT_DIR')
from utils.config_loader import get_config

config = get_config()
server_config = config.get_server_config()
print(server_config['port'])
")

PORT=$CONFIG_VALUES

print_info "Stopping vLLM server on port $PORT..."

# Method 1: Kill using saved PID
if [ -f "$PID_FILE" ]; then
    PID=$(cat "$PID_FILE")
    if kill -0 $PID 2>/dev/null; then
        print_info "Killing server with PID: $PID"
        kill -TERM $PID
        sleep 3
        
        # Force kill if still running
        if kill -0 $PID 2>/dev/null; then
            print_warning "Server still running, force killing..."
            kill -9 $PID
        fi
        
        print_info "Server stopped successfully"
    else
        print_warning "PID $PID is not running"
    fi
    
    rm -f "$PID_FILE"
else
    print_warning "PID file not found: $PID_FILE"
fi

# Method 2: Kill any process using the port
if lsof -Pi :$PORT -sTCP:LISTEN -t >/dev/null 2>&1; then
    print_info "Found process still using port $PORT, killing it..."
    PID=$(lsof -Pi :$PORT -sTCP:LISTEN -t)
    kill -9 $PID 2>/dev/null || true
    sleep 1
fi

# Verify port is free
if lsof -Pi :$PORT -sTCP:LISTEN -t >/dev/null 2>&1; then
    print_error "Failed to free port $PORT"
    exit 1
else
    print_info "Port $PORT is now free"
fi

print_info "vLLM server stopped successfully!"
