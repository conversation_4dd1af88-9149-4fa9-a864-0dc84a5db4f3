# Vidur Validation Framework

This framework validates the accuracy claims in the Vidur paper by comparing Vidur simulation results with actual vLLM performance.

## Paper Claim

> "We validate the fidelity of Vidur on several LLMs and show that it estimates inference latency with less than 9% error across the range."

## Validation Approach

1. **Run vLLM Benchmark**: Use vLLM's built-in benchmark with real datasets
2. **Extract Trace Data**: Extract token counts and timing from vLLM results  
3. **Run Vidur Simulation**: Use extracted trace to simulate the same workload
4. **Compare Results**: Analyze accuracy of Vidur's predictions

## Quick Start

### Prerequisites

- Python 3.8+
- vLLM installed and working
- Vidur repository cloned and set up
- Required Python packages: `pandas`, `matplotlib`, `seaborn`, `pyyaml`, `requests`

### Installation

```bash
# Install required packages
pip install pandas matplotlib seaborn pyyaml requests

# Make scripts executable
chmod +x scripts/*.sh
```

### Configuration

Edit `config.yaml` to match your setup:

```yaml
model:
  local_path: "/your/path/to/Meta-Llama-3-8B"  # Update this path
  
server:
  port: 8000  # Change if needed
  
benchmark:
  dataset_name: "sharegpt"  # Options: sharegpt, sonnet, random
  num_prompts: 512
  request_rate: 6.45
```

### Running Validation

#### Option 1: Full Automated Pipeline

```bash
# 1. Start vLLM server (in one terminal)
cd vidur_validation
./scripts/start_vllm_server.sh

# 2. Run full validation (in another terminal)
cd vidur_validation
python run_full_validation.py
```

#### Option 2: Step-by-Step

```bash
# 1. Start vLLM server
./scripts/start_vllm_server.sh

# 2. Run vLLM benchmark
python vllm_benchmark.py

# 3. Extract trace data
python trace_extractor.py

# 4. Run Vidur simulation
python vidur_runner.py

# 5. Compare results
python comparison_analyzer.py

# 6. Stop server when done
./scripts/stop_vllm_server.sh
```

## Output Structure

```
vidur_validation/
├── results/
│   ├── vllm/                    # vLLM benchmark results
│   │   ├── vllm_benchmark_results.json
│   │   └── vllm_extracted_trace.csv
│   ├── vidur/                   # Vidur simulation results
│   │   ├── request_metrics.csv
│   │   ├── config.json
│   │   └── plots/
│   └── comparison/              # Comparison analysis
│       ├── comparison_plots.png
│       └── comparison_report.txt
```

## Key Metrics Compared

- **End-to-End Latency**: Total request processing time
- **Time to First Token (TTFT)**: Prefill phase latency
- **Time per Output Token (TPOT)**: Decode phase per-token latency
- **Throughput**: Requests and tokens per second

## Validation Criteria

The validation passes if:
- Mean relative error < 9% for each key metric
- Strong correlation between vLLM and Vidur results
- Error distribution is reasonable

## Troubleshooting

### Common Issues

1. **vLLM Server Won't Start**
   ```bash
   # Check if port is in use
   lsof -i :8000
   
   # Kill existing process
   ./scripts/stop_vllm_server.sh
   ```

2. **Model Path Not Found**
   - Update `config.yaml` with correct model path
   - Ensure model files are accessible

3. **Benchmark Fails**
   - Check vLLM server is running: `curl http://localhost:8000/health`
   - Verify model is loaded correctly
   - Check server logs for errors

4. **Vidur Simulation Fails**
   - Ensure you're running from Vidur repository root
   - Check trace file was generated correctly
   - Verify Vidur dependencies are installed

### Debug Mode

For detailed debugging, run individual components:

```bash
# Test configuration loading
python -c "from utils.config_loader import get_config; print(get_config().get('model.name'))"

# Test server connection
python -c "import requests; print(requests.get('http://localhost:8000/health').status_code)"

# Check trace file
python -c "import pandas as pd; print(pd.read_csv('results/vllm/vllm_extracted_trace.csv').head())"
```

## Configuration Options

### Model Configuration
- `name`: Model identifier for vLLM
- `local_path`: Path to model weights
- `max_model_len`: Maximum sequence length
- `tensor_parallel_size`: Number of GPUs for tensor parallelism

### Benchmark Configuration
- `dataset_name`: Built-in dataset to use (sharegpt, sonnet, random, burstgpt)
- `num_prompts`: Number of requests to benchmark
- `request_rate`: Target QPS for load testing

### Hardware Configuration
- `device`: GPU type for Vidur simulation (a100, v100, etc.)
- `num_replicas`: Number of model replicas
- `num_pipeline_stages`: Pipeline parallelism stages

## Extending the Framework

### Adding New Metrics

1. Update `comparison_analyzer.py` to extract new metrics
2. Add metric mappings between vLLM and Vidur field names
3. Update error calculation and reporting

### Using Custom Datasets

1. Modify `vllm_benchmark.py` to support custom dataset paths
2. Update configuration schema in `config.yaml`
3. Ensure dataset format matches vLLM expectations

### Different Models

1. Update model configuration in `config.yaml`
2. Ensure model is compatible with both vLLM and Vidur
3. Adjust hardware configuration as needed

## Results Interpretation

### Successful Validation
- All metrics show < 9% mean relative error
- Strong correlation (> 0.8) between vLLM and Vidur
- Error distribution is centered around zero

### Failed Validation
- High relative errors indicate simulation inaccuracy
- Low correlation suggests systematic differences
- Investigate specific failure modes in detailed results

## Contributing

To contribute improvements:

1. Fork the repository
2. Create feature branch
3. Add tests for new functionality
4. Submit pull request with detailed description

## License

This validation framework is provided as-is for research purposes.
