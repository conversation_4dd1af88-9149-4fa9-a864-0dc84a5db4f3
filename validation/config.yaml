# Vidur Validation Configuration

# Model Configuration
model:
  name: "meta-llama/Meta-Llama-3-8B"
  local_path: "/share_data/llm_weights/Meta-Llama-3-8B"
  max_model_len: 16384
  tensor_parallel_size: 1

# Server Configuration
server:
  host: "localhost"
  port: 8000
  base_url: "http://localhost:8000"

# Benchmark Configuration
benchmark:
  dataset_name: "sharegpt"  # Options: sharegpt, sonnet, random, burstgpt
  num_prompts: 512
  request_rate: 6.45  # QPS
  backend: "vllm"

# Hardware Configuration
hardware:
  device: "a100"
  num_replicas: 1
  num_pipeline_stages: 1

# Vidur Scheduler Configuration
vidur_scheduler:
  type: "sarathi"
  batch_size_cap: 512
  chunk_size: 512

# Predictor Configuration
predictor:
  max_prefill_chunk_size: 16384
  max_batch_size: 512
  max_tokens_per_request: 16384

# Output Configuration
output:
  vllm_results_dir: "./results/vllm"
  vidur_results_dir: "./results/vidur"
  comparison_results_dir: "./results/comparison"
  vllm_results_file: "vllm_benchmark_results.json"
  extracted_trace_file: "vllm_extracted_trace.csv"

# Validation Configuration
validation:
  error_threshold: 9.0  # Maximum acceptable error percentage
  metrics_to_compare:
    - "e2e_latency"
    - "ttft"
    - "tpot"
    - "throughput"
