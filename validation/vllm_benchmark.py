#!/usr/bin/env python3
"""
vLLM Benchmark Runner
Runs vLLM benchmark and collects performance metrics
"""

import json
import time
import subprocess
import sys
from pathlib import Path
from typing import Dict, Any, List
import requests

from utils.config_loader import get_config


class VLLMBenchmarkRunner:
    """Run vLLM benchmark and collect results"""
    
    def __init__(self):
        self.config = get_config()
        self.config.create_output_dirs()
        
        # Get configurations
        self.benchmark_config = self.config.get_benchmark_config()
        self.server_config = self.config.get_server_config()
        self.output_config = self.config.get_output_config()
        self.model_config = self.config.get_model_config()
    
    def check_server_health(self) -> bool:
        """Check if vLLM server is running and healthy"""
        try:
            health_url = f"{self.server_config['base_url']}/health"
            response = requests.get(health_url, timeout=5)
            return response.status_code == 200
        except Exception as e:
            print(f"Server health check failed: {e}")
            return False
    
    def wait_for_server(self, max_wait: int = 60) -> bool:
        """Wait for server to be ready"""
        print("Waiting for vLLM server to be ready...")
        
        for i in range(max_wait):
            if self.check_server_health():
                print("✓ Server is ready!")
                return True
            
            print(f"Waiting... ({i+1}/{max_wait})")
            time.sleep(1)
        
        print("✗ Server failed to become ready")
        return False
    
    def run_benchmark(self) -> Dict[str, Any]:
        """Run vLLM benchmark using benchmark_serving.py"""
        
        if not self.wait_for_server():
            raise RuntimeError("vLLM server is not ready")
        
        # Prepare benchmark command
        results_file = Path(self.output_config['vllm_results_dir']) / self.output_config['vllm_results_file']
        results_file = results_file.resolve()  # Use absolute path

        # Try to find vLLM benchmark script
        benchmark_script = None
        possible_paths = [
            "benchmarks/benchmark_serving.py",  # If running from vLLM repo
            Path.home() / ".local/lib/python*/site-packages/vllm/benchmarks/benchmark_serving.py",  # pip install
            "/opt/conda/lib/python*/site-packages/vllm/benchmarks/benchmark_serving.py"  # conda install
        ]

        # First try the standard path
        benchmark_script = "benchmarks/benchmark_serving.py"

        cmd = [
            "python", benchmark_script,
            "--backend", self.benchmark_config['backend'],
            "--model", self.model_config['name'],
            "--dataset-name", self.benchmark_config['dataset_name'],
            "--num-prompts", str(self.benchmark_config['num_prompts']),
            "--request-rate", str(self.benchmark_config['request_rate']),
            "--output-json", str(results_file),
            "--save-result"
        ]
        
        print(f"Running benchmark command: {' '.join(cmd)}")
        print(f"Results will be saved to: {results_file}")
        
        # Run benchmark
        try:
            print("Starting benchmark... This may take several minutes.")
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                check=True,
                timeout=1800  # 30 minutes timeout
            )

            print("✓ Benchmark completed successfully!")
            if result.stdout:
                print("STDOUT (last 1000 chars):", result.stdout[-1000:])

            if result.stderr:
                print("STDERR (last 1000 chars):", result.stderr[-1000:])

        except subprocess.TimeoutExpired:
            raise RuntimeError("Benchmark timed out after 30 minutes")
        except subprocess.CalledProcessError as e:
            print(f"✗ Benchmark failed with return code {e.returncode}")
            if e.stdout:
                print("STDOUT:", e.stdout[-1000:])
            if e.stderr:
                print("STDERR:", e.stderr[-1000:])
            raise RuntimeError(f"Benchmark failed: {e}")
        
        # Load and return results
        return self.load_results(results_file)
    
    def load_results(self, results_file: Path) -> Dict[str, Any]:
        """Load benchmark results from JSON file"""
        try:
            with open(results_file, 'r') as f:
                results = json.load(f)
            
            print(f"✓ Results loaded from {results_file}")
            return results
            
        except FileNotFoundError:
            raise FileNotFoundError(f"Results file not found: {results_file}")
        except json.JSONDecodeError as e:
            raise ValueError(f"Failed to parse results JSON: {e}")
    
    def print_summary(self, results: Dict[str, Any]):
        """Print benchmark results summary"""
        print("\n" + "="*60)
        print("vLLM BENCHMARK RESULTS SUMMARY")
        print("="*60)
        
        # Extract key metrics
        if 'summary' in results:
            summary = results['summary']
            print(f"Total Requests: {summary.get('num_requests', 'N/A')}")
            print(f"Total Time: {summary.get('total_time', 'N/A'):.2f}s")
            print(f"Requests/sec: {summary.get('request_throughput', 'N/A'):.2f}")
            print(f"Tokens/sec: {summary.get('output_throughput', 'N/A'):.2f}")
            
            print(f"\nLatency Metrics:")
            print(f"  Mean TTFT: {summary.get('mean_ttft_ms', 'N/A'):.2f}ms")
            print(f"  Mean TPOT: {summary.get('mean_tpot_ms', 'N/A'):.2f}ms")
            print(f"  Mean E2E: {summary.get('mean_e2e_latency_ms', 'N/A'):.2f}ms")
            
            print(f"\nP99 Latency Metrics:")
            print(f"  P99 TTFT: {summary.get('p99_ttft_ms', 'N/A'):.2f}ms")
            print(f"  P99 TPOT: {summary.get('p99_tpot_ms', 'N/A'):.2f}ms")
            print(f"  P99 E2E: {summary.get('p99_e2e_latency_ms', 'N/A'):.2f}ms")
        
        print("="*60)


def main():
    """Main function"""
    print("Starting vLLM Benchmark Runner...")
    
    try:
        runner = VLLMBenchmarkRunner()
        results = runner.run_benchmark()
        runner.print_summary(results)
        
        print(f"\n✓ Benchmark completed successfully!")
        print(f"Results saved to: {runner.output_config['vllm_results_dir']}")
        
    except Exception as e:
        print(f"✗ Benchmark failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
