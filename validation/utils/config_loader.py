#!/usr/bin/env python3
"""
Configuration loader utility for Vidur validation
"""

import yaml
import os
from pathlib import Path
from typing import Dict, Any


class ConfigLoader:
    """Load and manage configuration from YAML file"""
    
    def __init__(self, config_path: str = None):
        if config_path is None:
            # Default to config.yaml in the parent directory
            config_path = Path(__file__).parent.parent / "config.yaml"
        
        self.config_path = Path(config_path)
        self.config = self._load_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from YAML file"""
        try:
            with open(self.config_path, 'r') as f:
                config = yaml.safe_load(f)
            return config
        except FileNotFoundError:
            raise FileNotFoundError(f"Configuration file not found: {self.config_path}")
        except yaml.YAMLError as e:
            raise ValueError(f"Error parsing YAML configuration: {e}")
    
    def get(self, key_path: str, default=None):
        """
        Get configuration value using dot notation
        Example: get('model.name') returns config['model']['name']
        """
        keys = key_path.split('.')
        value = self.config
        
        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default
    
    def get_model_config(self) -> Dict[str, Any]:
        """Get model configuration"""
        return self.config.get('model', {})
    
    def get_server_config(self) -> Dict[str, Any]:
        """Get server configuration"""
        return self.config.get('server', {})
    
    def get_benchmark_config(self) -> Dict[str, Any]:
        """Get benchmark configuration"""
        return self.config.get('benchmark', {})
    
    def get_hardware_config(self) -> Dict[str, Any]:
        """Get hardware configuration"""
        return self.config.get('hardware', {})
    
    def get_vidur_scheduler_config(self) -> Dict[str, Any]:
        """Get Vidur scheduler configuration"""
        return self.config.get('vidur_scheduler', {})
    
    def get_predictor_config(self) -> Dict[str, Any]:
        """Get predictor configuration"""
        return self.config.get('predictor', {})
    
    def get_output_config(self) -> Dict[str, Any]:
        """Get output configuration"""
        return self.config.get('output', {})
    
    def get_validation_config(self) -> Dict[str, Any]:
        """Get validation configuration"""
        return self.config.get('validation', {})
    
    def create_output_dirs(self):
        """Create output directories if they don't exist"""
        output_config = self.get_output_config()
        
        dirs_to_create = [
            output_config.get('vllm_results_dir', './results/vllm'),
            output_config.get('vidur_results_dir', './results/vidur'),
            output_config.get('comparison_results_dir', './results/comparison')
        ]
        
        for dir_path in dirs_to_create:
            Path(dir_path).mkdir(parents=True, exist_ok=True)
            print(f"Created directory: {dir_path}")


# Global config instance
_config_instance = None

def get_config(config_path: str = None) -> ConfigLoader:
    """Get global configuration instance"""
    global _config_instance
    if _config_instance is None:
        _config_instance = ConfigLoader(config_path)
    return _config_instance


if __name__ == "__main__":
    # Test configuration loading
    config = get_config()
    print("Configuration loaded successfully!")
    print(f"Model name: {config.get('model.name')}")
    print(f"Server port: {config.get('server.port')}")
    print(f"Benchmark dataset: {config.get('benchmark.dataset_name')}")
    
    # Create output directories
    config.create_output_dirs()
