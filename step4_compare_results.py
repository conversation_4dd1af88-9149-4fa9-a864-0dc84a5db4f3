#!/usr/bin/env python3
"""
Step 4: Compare vLLM and Vidur Results
This script compares the performance metrics from vLLM and Vidur simulations
and generates a detailed analysis report.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import argparse
from pathlib import Path
import json

# Colors for output
RED = '\033[0;31m'
GREEN = '\033[0;32m'
YELLOW = '\033[1;33m'
BLUE = '\033[0;34m'
NC = '\033[0m'

def print_info(msg):
    print(f"{GREEN}[INFO]{NC} {msg}")

def print_warning(msg):
    print(f"{YELLOW}[WARNING]{NC} {msg}")

def print_error(msg):
    print(f"{RED}[ERROR]{NC} {msg}")

def print_step(msg):
    print(f"{BLUE}[STEP]{NC} {msg}")

class ResultsComparator:
    def __init__(self, vllm_metrics_file, vidur_results_dir):
        self.vllm_metrics_file = Path(vllm_metrics_file)
        self.vidur_results_dir = Path(vidur_results_dir)
        self.output_dir = Path("comparison_results")
        self.output_dir.mkdir(exist_ok=True)
        
        # 设置绘图样式
        plt.style.use('default')
        sns.set_palette("husl")
    
    def load_data(self):
        """加载 vLLM 和 Vidur 的结果数据"""
        print_step("Loading vLLM and Vidur results...")
        
        # 加载 vLLM 指标
        if not self.vllm_metrics_file.exists():
            raise FileNotFoundError(f"vLLM metrics file not found: {self.vllm_metrics_file}")
        
        self.vllm_df = pd.read_csv(self.vllm_metrics_file)
        print_info(f"Loaded vLLM metrics: {len(self.vllm_df)} requests")
        
        # 加载 Vidur 结果
        vidur_metrics_file = self.vidur_results_dir / "request_metrics.csv"
        if not vidur_metrics_file.exists():
            raise FileNotFoundError(f"Vidur metrics file not found: {vidur_metrics_file}")
        
        self.vidur_df = pd.read_csv(vidur_metrics_file)
        print_info(f"Loaded Vidur metrics: {len(self.vidur_df)} requests")
        
        # 确保数据长度一致
        min_len = min(len(self.vllm_df), len(self.vidur_df))
        if len(self.vllm_df) != len(self.vidur_df):
            print_warning(f"Data length mismatch: vLLM={len(self.vllm_df)}, Vidur={len(self.vidur_df)}")
            print_warning(f"Using first {min_len} requests for comparison")
            self.vllm_df = self.vllm_df.head(min_len)
            self.vidur_df = self.vidur_df.head(min_len)
    
    def align_metrics(self):
        """对齐和标准化指标名称"""
        print_step("Aligning metrics...")
        
        # 创建对比数据框
        self.comparison_df = pd.DataFrame()
        
        # 基本信息
        self.comparison_df['request_id'] = range(len(self.vllm_df))
        self.comparison_df['prompt_len'] = self.vllm_df['prompt_len'].values
        self.comparison_df['output_len'] = self.vllm_df['output_len'].values
        
        # vLLM 指标
        self.comparison_df['vllm_ttft'] = self.vllm_df['ttft'].values
        self.comparison_df['vllm_tpot'] = self.vllm_df['tpot'].values
        self.comparison_df['vllm_e2e'] = self.vllm_df['e2e_latency'].values
        
        # Vidur 指标
        self.comparison_df['vidur_ttft'] = self.vidur_df['prefill_e2e_time'].values
        
        # 计算 Vidur 的 TPOT
        decode_time = self.vidur_df['decode_time'].values
        output_tokens = self.vidur_df['num_decode_tokens'].values
        self.comparison_df['vidur_tpot'] = np.where(
            output_tokens > 1,
            decode_time / (output_tokens - 1),
            decode_time
        )
        
        self.comparison_df['vidur_e2e'] = self.vidur_df['request_e2e_time'].values
        
        # 过滤无效数据
        valid_mask = (
            (self.comparison_df['vllm_ttft'] > 0) &
            (self.comparison_df['vllm_e2e'] > 0) &
            (self.comparison_df['vidur_ttft'] > 0) &
            (self.comparison_df['vidur_e2e'] > 0)
        )
        
        self.comparison_df = self.comparison_df[valid_mask].reset_index(drop=True)
        print_info(f"Valid requests for comparison: {len(self.comparison_df)}")
    
    def calculate_errors(self):
        """计算各项指标的误差"""
        print_step("Calculating errors...")
        
        # 计算相对误差 (%)
        self.comparison_df['ttft_error'] = np.abs(
            self.comparison_df['vidur_ttft'] - self.comparison_df['vllm_ttft']
        ) / self.comparison_df['vllm_ttft'] * 100
        
        self.comparison_df['tpot_error'] = np.abs(
            self.comparison_df['vidur_tpot'] - self.comparison_df['vllm_tpot']
        ) / self.comparison_df['vllm_tpot'] * 100
        
        self.comparison_df['e2e_error'] = np.abs(
            self.comparison_df['vidur_e2e'] - self.comparison_df['vllm_e2e']
        ) / self.comparison_df['vllm_e2e'] * 100
        
        # 过滤异常值（误差超过1000%的可能是数据问题）
        for col in ['ttft_error', 'tpot_error', 'e2e_error']:
            outlier_mask = self.comparison_df[col] > 1000
            if outlier_mask.any():
                print_warning(f"Filtering {outlier_mask.sum()} outliers in {col}")
                self.comparison_df.loc[outlier_mask, col] = np.nan
    
    def generate_summary_stats(self):
        """生成汇总统计"""
        print_step("Generating summary statistics...")
        
        self.summary_stats = {}
        
        for metric in ['ttft', 'tpot', 'e2e']:
            error_col = f'{metric}_error'
            valid_errors = self.comparison_df[error_col].dropna()
            
            if len(valid_errors) > 0:
                self.summary_stats[metric] = {
                    'mean_error': valid_errors.mean(),
                    'median_error': valid_errors.median(),
                    'p95_error': valid_errors.quantile(0.95),
                    'max_error': valid_errors.max(),
                    'std_error': valid_errors.std(),
                    'count': len(valid_errors)
                }
            else:
                self.summary_stats[metric] = {
                    'mean_error': np.nan,
                    'median_error': np.nan,
                    'p95_error': np.nan,
                    'max_error': np.nan,
                    'std_error': np.nan,
                    'count': 0
                }
    
    def create_visualizations(self):
        """创建可视化图表"""
        print_step("Creating visualizations...")
        
        # 创建子图
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('vLLM vs Vidur Performance Comparison', fontsize=16)
        
        metrics = ['ttft', 'tpot', 'e2e']
        metric_names = ['Time to First Token (TTFT)', 'Time per Output Token (TPOT)', 'End-to-End Latency']
        
        for i, (metric, name) in enumerate(zip(metrics, metric_names)):
            # 散点图：实际值对比
            ax1 = axes[0, i]
            vllm_col = f'vllm_{metric}'
            vidur_col = f'vidur_{metric}'
            
            ax1.scatter(self.comparison_df[vllm_col], self.comparison_df[vidur_col], 
                       alpha=0.6, s=20)
            
            # 添加 y=x 参考线
            min_val = min(self.comparison_df[vllm_col].min(), self.comparison_df[vidur_col].min())
            max_val = max(self.comparison_df[vllm_col].max(), self.comparison_df[vidur_col].max())
            ax1.plot([min_val, max_val], [min_val, max_val], 'r--', alpha=0.8, label='Perfect Match')
            
            ax1.set_xlabel(f'vLLM {name}')
            ax1.set_ylabel(f'Vidur {name}')
            ax1.set_title(f'{name} Comparison')
            ax1.legend()
            ax1.grid(True, alpha=0.3)
            
            # 误差分布直方图
            ax2 = axes[1, i]
            error_col = f'{metric}_error'
            valid_errors = self.comparison_df[error_col].dropna()
            
            if len(valid_errors) > 0:
                ax2.hist(valid_errors, bins=30, alpha=0.7, edgecolor='black')
                ax2.axvline(valid_errors.mean(), color='red', linestyle='--', 
                           label=f'Mean: {valid_errors.mean():.1f}%')
                ax2.axvline(valid_errors.median(), color='green', linestyle='--',
                           label=f'Median: {valid_errors.median():.1f}%')
            
            ax2.set_xlabel('Relative Error (%)')
            ax2.set_ylabel('Frequency')
            ax2.set_title(f'{name} Error Distribution')
            ax2.legend()
            ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # 保存图表
        plot_file = self.output_dir / "performance_comparison.png"
        plt.savefig(plot_file, dpi=300, bbox_inches='tight')
        print_info(f"Visualization saved to: {plot_file}")
        
        plt.close()
    
    def generate_report(self):
        """生成详细报告"""
        print_step("Generating comparison report...")
        
        report_file = self.output_dir / "comparison_report.txt"
        
        with open(report_file, 'w') as f:
            f.write("vLLM vs Vidur Performance Comparison Report\n")
            f.write("=" * 50 + "\n\n")
            
            f.write(f"Data Summary:\n")
            f.write(f"  Total requests compared: {len(self.comparison_df)}\n")
            f.write(f"  vLLM metrics file: {self.vllm_metrics_file}\n")
            f.write(f"  Vidur results dir: {self.vidur_results_dir}\n\n")
            
            f.write("Error Analysis:\n")
            f.write("-" * 20 + "\n")
            
            for metric, stats in self.summary_stats.items():
                metric_name = {
                    'ttft': 'Time to First Token (TTFT)',
                    'tpot': 'Time per Output Token (TPOT)', 
                    'e2e': 'End-to-End Latency'
                }[metric]
                
                f.write(f"\n{metric_name}:\n")
                f.write(f"  Mean Error: {stats['mean_error']:.2f}%\n")
                f.write(f"  Median Error: {stats['median_error']:.2f}%\n")
                f.write(f"  95th Percentile Error: {stats['p95_error']:.2f}%\n")
                f.write(f"  Maximum Error: {stats['max_error']:.2f}%\n")
                f.write(f"  Standard Deviation: {stats['std_error']:.2f}%\n")
                f.write(f"  Valid Samples: {stats['count']}\n")
            
            # 验证论文声明
            f.write(f"\nPaper Validation:\n")
            f.write("-" * 20 + "\n")
            f.write("Paper claim: 'Vidur estimates inference latency with less than 9% error'\n\n")
            
            e2e_mean_error = self.summary_stats['e2e']['mean_error']
            if not np.isnan(e2e_mean_error):
                if e2e_mean_error < 9.0:
                    f.write(f"✓ VALIDATED: Mean E2E latency error is {e2e_mean_error:.2f}% < 9%\n")
                else:
                    f.write(f"✗ NOT VALIDATED: Mean E2E latency error is {e2e_mean_error:.2f}% >= 9%\n")
            else:
                f.write("? INCONCLUSIVE: Unable to calculate E2E latency error\n")
        
        print_info(f"Report saved to: {report_file}")
        
        # 保存详细数据
        data_file = self.output_dir / "detailed_comparison.csv"
        self.comparison_df.to_csv(data_file, index=False)
        print_info(f"Detailed data saved to: {data_file}")
    
    def print_summary(self):
        """打印汇总结果到控制台"""
        print_step("Summary Results:")
        
        print(f"\n{'Metric':<20} {'Mean Error':<12} {'Median Error':<14} {'P95 Error':<12} {'Max Error':<12}")
        print("-" * 70)
        
        for metric, stats in self.summary_stats.items():
            metric_name = {
                'ttft': 'TTFT',
                'tpot': 'TPOT',
                'e2e': 'E2E Latency'
            }[metric]
            
            print(f"{metric_name:<20} {stats['mean_error']:<12.2f} {stats['median_error']:<14.2f} "
                  f"{stats['p95_error']:<12.2f} {stats['max_error']:<12.2f}")
        
        # 验证结果
        print(f"\n{BLUE}Paper Validation:{NC}")
        e2e_mean_error = self.summary_stats['e2e']['mean_error']
        if not np.isnan(e2e_mean_error):
            if e2e_mean_error < 9.0:
                print(f"{GREEN}✓ VALIDATED:{NC} Mean E2E latency error is {e2e_mean_error:.2f}% < 9%")
            else:
                print(f"{RED}✗ NOT VALIDATED:{NC} Mean E2E latency error is {e2e_mean_error:.2f}% >= 9%")
        else:
            print(f"{YELLOW}? INCONCLUSIVE:{NC} Unable to calculate E2E latency error")

def find_latest_files():
    """查找最新的文件"""
    # 查找 vLLM metrics 文件
    input_dir = Path("vidur_input_data")
    vllm_files = list(input_dir.glob("vllm_metrics_*.csv")) if input_dir.exists() else []
    vllm_file = max(vllm_files, key=lambda x: x.stat().st_mtime) if vllm_files else None
    
    # 查找 Vidur 结果目录
    simulator_output = Path("simulator_output")
    if simulator_output.exists():
        output_dirs = [d for d in simulator_output.iterdir() if d.is_dir()]
        vidur_dir = max(output_dirs, key=lambda x: x.stat().st_mtime) if output_dirs else None
    else:
        vidur_dir = None
    
    return vllm_file, vidur_dir

def main():
    parser = argparse.ArgumentParser(description="Compare vLLM and Vidur results")
    parser.add_argument("--vllm-metrics", 
                       help="vLLM metrics CSV file")
    parser.add_argument("--vidur-results",
                       help="Vidur results directory")
    
    args = parser.parse_args()
    
    # 确定输入文件
    if args.vllm_metrics and args.vidur_results:
        vllm_file = Path(args.vllm_metrics)
        vidur_dir = Path(args.vidur_results)
    else:
        vllm_file, vidur_dir = find_latest_files()
        
        if vllm_file is None:
            print_error("No vLLM metrics file found. Please run previous steps first.")
            return 1
        
        if vidur_dir is None:
            print_error("No Vidur results directory found. Please run step3_run_vidur_simulation.py first.")
            return 1
        
        print_info(f"Using vLLM metrics: {vllm_file}")
        print_info(f"Using Vidur results: {vidur_dir}")
    
    try:
        # 创建比较器
        comparator = ResultsComparator(vllm_file, vidur_dir)
        
        # 执行比较流程
        comparator.load_data()
        comparator.align_metrics()
        comparator.calculate_errors()
        comparator.generate_summary_stats()
        comparator.create_visualizations()
        comparator.generate_report()
        comparator.print_summary()
        
        print_info("Comparison completed successfully!")
        print_info(f"Results saved in: {comparator.output_dir}")
        
        return 0
        
    except Exception as e:
        print_error(f"Comparison failed: {e}")
        return 1

if __name__ == "__main__":
    exit(main())
