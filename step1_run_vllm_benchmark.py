#!/usr/bin/env python3
"""
Step 1: Run vLLM Benchmark
This script starts a vLLM server and runs benchmark tests to collect performance data.
"""

import subprocess
import time
import json
import os
import signal
import sys
import argparse
from pathlib import Path

# Colors for output
RED = '\033[0;31m'
GREEN = '\033[0;32m'
YELLOW = '\033[1;33m'
BLUE = '\033[0;34m'
NC = '\033[0m'  # No Color

def print_info(msg):
    print(f"{GREEN}[INFO]{NC} {msg}")

def print_warning(msg):
    print(f"{YELLOW}[WARNING]{NC} {msg}")

def print_error(msg):
    print(f"{RED}[ERROR]{NC} {msg}")

def print_step(msg):
    print(f"{BLUE}[STEP]{NC} {msg}")

class VLLMBenchmark:
    def __init__(self, model_name="meta-llama/Meta-Llama-3-8B", 
                 tensor_parallel_size=1, max_model_len=16384,
                 port=8000):
        self.model_name = model_name
        self.tensor_parallel_size = tensor_parallel_size
        self.max_model_len = max_model_len
        self.port = port
        self.server_process = None
        self.results_dir = Path("vllm_benchmark_results")
        self.results_dir.mkdir(exist_ok=True)
    
    def start_server(self):
        """启动 vLLM 服务器"""
        print_step("Starting vLLM server...")

        cmd = [
            "python", "-m", "vllm.entrypoints.openai.api_server",
            "--model", self.model_name,
            "--tensor-parallel-size", str(self.tensor_parallel_size),
            "--max-model-len", str(self.max_model_len),
            "--port", str(self.port),
            "--disable-log-requests",  # 减少日志输出
            "--trust-remote-code"  # 允许远程代码（某些模型需要）
        ]

        print_info(f"Server command: {' '.join(cmd)}")
        print_info("Note: First-time model loading may take 5-15 minutes depending on model size and network speed")

        # 创建日志文件
        log_file = self.results_dir / "vllm_server.log"

        try:
            with open(log_file, 'w') as f:
                self.server_process = subprocess.Popen(
                    cmd,
                    stdout=f,
                    stderr=subprocess.STDOUT,
                    text=True
                )

            print_info(f"Server logs will be written to: {log_file}")

            # 等待服务器启动
            self._wait_for_server()
            print_info("vLLM server started successfully!")

        except Exception as e:
            print_error(f"Failed to start vLLM server: {e}")

            # 打印服务器日志的最后几行
            if log_file.exists():
                print_error("Last few lines of server log:")
                try:
                    with open(log_file, 'r') as f:
                        lines = f.readlines()
                        for line in lines[-10:]:  # 最后10行
                            print(f"  {line.rstrip()}")
                except:
                    pass

            raise
    
    def _wait_for_server(self, timeout=600):
        """等待服务器启动"""
        import requests

        start_time = time.time()
        check_interval = 10  # 增加检查间隔
        last_log_time = start_time

        print_info("Waiting for server to start (this may take several minutes for first-time model loading)...")

        while time.time() - start_time < timeout:
            try:
                # 尝试多个健康检查端点
                endpoints = [f"http://localhost:{self.port}/health",
                           f"http://localhost:{self.port}/v1/models"]

                for endpoint in endpoints:
                    try:
                        response = requests.get(endpoint, timeout=10)
                        if response.status_code == 200:
                            print_info(f"Server responded successfully at {endpoint}")
                            return
                    except requests.exceptions.RequestException:
                        continue

            except Exception as e:
                pass

            # 每30秒打印一次状态
            current_time = time.time()
            if current_time - last_log_time > 30:
                elapsed = current_time - start_time
                print_info(f"Still waiting... ({elapsed:.0f}s elapsed, timeout in {timeout-elapsed:.0f}s)")
                last_log_time = current_time

            print(".", end="", flush=True)
            time.sleep(check_interval)

        print()
        raise TimeoutError(f"Server failed to start within {timeout} seconds. "
                          "This might be due to model downloading or insufficient resources.")
    
    def run_benchmark(self, dataset_name="sharegpt", num_prompts=512, 
                     request_rate=6.45, output_file=None):
        """运行基准测试"""
        print_step(f"Running benchmark with {dataset_name} dataset...")
        
        if output_file is None:
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            output_file = self.results_dir / f"vllm_results_{dataset_name}_{timestamp}.json"
        
        cmd = [
            "python", "benchmarks/benchmark_serving.py",
            "--backend", "vllm",
            "--model", self.model_name,
            "--dataset-name", dataset_name,
            "--num-prompts", str(num_prompts),
            "--request-rate", str(request_rate),
            "--output-json", str(output_file),
            "--save-result"
        ]
        
        print_info(f"Benchmark command: {' '.join(cmd)}")
        
        try:
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=1800  # 30分钟超时
            )
            
            if result.returncode == 0:
                print_info("Benchmark completed successfully!")
                print_info(f"Results saved to: {output_file}")
                return str(output_file)
            else:
                print_error("Benchmark failed!")
                print_error(f"STDOUT: {result.stdout}")
                print_error(f"STDERR: {result.stderr}")
                raise RuntimeError("Benchmark execution failed")
                
        except subprocess.TimeoutExpired:
            print_error("Benchmark timed out!")
            raise
        except Exception as e:
            print_error(f"Error running benchmark: {e}")
            raise
    
    def stop_server(self):
        """停止 vLLM 服务器"""
        if self.server_process:
            print_step("Stopping vLLM server...")
            self.server_process.terminate()
            
            # 等待进程结束
            try:
                self.server_process.wait(timeout=10)
            except subprocess.TimeoutExpired:
                print_warning("Server didn't stop gracefully, killing...")
                self.server_process.kill()
                self.server_process.wait()
            
            print_info("vLLM server stopped.")
    
    def __enter__(self):
        self.start_server()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.stop_server()

def main():
    parser = argparse.ArgumentParser(description="Run vLLM benchmark test")
    parser.add_argument("--model", default="meta-llama/Meta-Llama-3-8B",
                       help="Model name to benchmark")
    parser.add_argument("--dataset", default="sharegpt",
                       choices=["sharegpt", "sonnet", "random", "burstgpt"],
                       help="Dataset to use for benchmarking")
    parser.add_argument("--num-prompts", type=int, default=512,
                       help="Number of prompts to test")
    parser.add_argument("--request-rate", type=float, default=6.45,
                       help="Request rate (QPS)")
    parser.add_argument("--tensor-parallel-size", type=int, default=1,
                       help="Tensor parallel size")
    parser.add_argument("--max-model-len", type=int, default=16384,
                       help="Maximum model length")
    parser.add_argument("--port", type=int, default=8000,
                       help="Server port")
    
    args = parser.parse_args()
    
    print_info("Starting vLLM benchmark process...")
    print_info(f"Model: {args.model}")
    print_info(f"Dataset: {args.dataset}")
    print_info(f"Number of prompts: {args.num_prompts}")
    print_info(f"Request rate: {args.request_rate} QPS")
    
    try:
        with VLLMBenchmark(
            model_name=args.model,
            tensor_parallel_size=args.tensor_parallel_size,
            max_model_len=args.max_model_len,
            port=args.port
        ) as benchmark:
            
            results_file = benchmark.run_benchmark(
                dataset_name=args.dataset,
                num_prompts=args.num_prompts,
                request_rate=args.request_rate
            )
            
            print_info(f"Benchmark completed! Results saved to: {results_file}")
            print_info("Next step: Run 'python step2_extract_data.py' to extract data for Vidur")
            
            return results_file
            
    except KeyboardInterrupt:
        print_warning("Benchmark interrupted by user")
        sys.exit(1)
    except Exception as e:
        print_error(f"Benchmark failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
