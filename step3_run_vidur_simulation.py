#!/usr/bin/env python3
"""
Step 3: Run Vidur Simulation
This script runs Vidur simulation using the extracted trace data from vLLM benchmark.
"""

import subprocess
import argparse
import glob
from pathlib import Path
import pandas as pd
import time

# Colors for output
RED = '\033[0;31m'
GREEN = '\033[0;32m'
YELLOW = '\033[1;33m'
BLUE = '\033[0;34m'
NC = '\033[0m'

def print_info(msg):
    print(f"{GREEN}[INFO]{NC} {msg}")

def print_warning(msg):
    print(f"{YELLOW}[WARNING]{NC} {msg}")

def print_error(msg):
    print(f"{RED}[ERROR]{NC} {msg}")

def print_step(msg):
    print(f"{BLUE}[STEP]{NC} {msg}")

class VidurSimulator:
    def __init__(self, trace_file, model_name="/share_data/llm_weights/Meta-Llama-3.1-8B"):
        self.trace_file = Path(trace_file)
        self.model_name = model_name
        self.output_dir = Path("vidur_simulation_results")
        self.output_dir.mkdir(exist_ok=True)
        
        # 验证 trace 文件
        self._validate_trace_file()
    
    def _validate_trace_file(self):
        """验证 trace 文件格式"""
        print_step("Validating trace file...")
        
        if not self.trace_file.exists():
            raise FileNotFoundError(f"Trace file not found: {self.trace_file}")
        
        try:
            df = pd.read_csv(self.trace_file)
            required_columns = ['arrived_at', 'num_prefill_tokens', 'num_decode_tokens']
            
            for col in required_columns:
                if col not in df.columns:
                    raise ValueError(f"Missing required column: {col}")
            
            print_info(f"Trace file validated successfully: {len(df)} requests")
            
            # 打印基本统计
            print_info(f"Time span: {df['arrived_at'].min():.2f}s - {df['arrived_at'].max():.2f}s")
            print_info(f"Prefill tokens: {df['num_prefill_tokens'].min()}-{df['num_prefill_tokens'].max()}")
            print_info(f"Decode tokens: {df['num_decode_tokens'].min()}-{df['num_decode_tokens'].max()}")
            
        except Exception as e:
            raise ValueError(f"Invalid trace file format: {e}")
    
    def run_simulation(self, 
                      device="a100",
                      num_replicas=1,
                      tensor_parallel_size=1,
                      num_pipeline_stages=1,
                      scheduler_type="sarathi",
                      batch_size_cap=512,
                      chunk_size=512,
                      max_tokens=16384):
        """运行 Vidur 仿真"""
        
        print_step("Running Vidur simulation...")
        
        # 读取 trace 文件获取请求数量
        df = pd.read_csv(self.trace_file)
        num_requests = len(df)
        
        print_info(f"Configuration:")
        print_info(f"  Model: {self.model_name}")
        print_info(f"  Device: {device}")
        print_info(f"  Requests: {num_requests}")
        print_info(f"  Scheduler: {scheduler_type}")
        print_info(f"  Batch size cap: {batch_size_cap}")
        print_info(f"  Chunk size: {chunk_size}")
        
        # 构建 Vidur 命令
        cmd = [
            "python", "-m", "vidur.main",
            
            # 模型和硬件配置
            "--replica_config_device", device,
            "--replica_config_model_name", self.model_name,
            "--cluster_config_num_replicas", str(num_replicas),
            "--replica_config_tensor_parallel_size", str(tensor_parallel_size),
            "--replica_config_num_pipeline_stages", str(num_pipeline_stages),
            
            # 请求生成配置
            "--request_generator_config_type", "synthetic",
            "--synthetic_request_generator_config_num_requests", str(num_requests),
            
            # 长度生成器配置（使用 trace 文件）
            "--length_generator_config_type", "trace",
            "--trace_request_length_generator_config_max_tokens", str(max_tokens),
            "--trace_request_length_generator_config_trace_file", str(self.trace_file),
            
            # 间隔生成器配置（使用 trace 文件的时间）
            "--interval_generator_config_type", "trace",
            "--trace_request_interval_generator_config_trace_file", str(self.trace_file),
            
            # 调度器配置
            "--replica_scheduler_config_type", scheduler_type,
            f"--{scheduler_type}_scheduler_config_batch_size_cap", str(batch_size_cap),
            f"--{scheduler_type}_scheduler_config_chunk_size", str(chunk_size),
            
            # 执行时间预测器配置
            "--random_forrest_execution_time_predictor_config_prediction_max_prefill_chunk_size", str(max_tokens),
            "--random_forrest_execution_time_predictor_config_prediction_max_batch_size", str(batch_size_cap),
            "--random_forrest_execution_time_predictor_config_prediction_max_tokens_per_request", str(max_tokens)
        ]
        
        print_info(f"Vidur command: {' '.join(cmd)}")
        
        try:
            # 记录开始时间
            start_time = time.time()
            
            # 运行 Vidur 仿真
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=3600  # 1小时超时
            )
            
            duration = time.time() - start_time
            
            if result.returncode == 0:
                print_info(f"Vidur simulation completed successfully in {duration:.1f}s!")
                
                # 查找输出目录
                output_dir = self._find_latest_output_dir()
                if output_dir:
                    print_info(f"Results saved to: {output_dir}")
                    return str(output_dir)
                else:
                    print_warning("Could not locate simulation output directory")
                    return None
                    
            else:
                print_error("Vidur simulation failed!")
                print_error(f"STDOUT: {result.stdout}")
                print_error(f"STDERR: {result.stderr}")
                raise RuntimeError("Vidur simulation execution failed")
                
        except subprocess.TimeoutExpired:
            print_error("Vidur simulation timed out!")
            raise
        except Exception as e:
            print_error(f"Error running Vidur simulation: {e}")
            raise
    
    def _find_latest_output_dir(self):
        """查找最新的 simulator_output 目录"""
        simulator_output = Path("simulator_output")
        if not simulator_output.exists():
            return None
        
        # 查找最新的输出目录
        output_dirs = [d for d in simulator_output.iterdir() if d.is_dir()]
        if not output_dirs:
            return None
        
        latest_dir = max(output_dirs, key=lambda x: x.stat().st_mtime)
        return latest_dir

def find_latest_trace_file():
    """查找最新的 trace 文件"""
    input_dir = Path("vidur_input_data")
    if not input_dir.exists():
        return None
    
    trace_files = list(input_dir.glob("vidur_trace_*.csv"))
    if not trace_files:
        return None
    
    return max(trace_files, key=lambda x: x.stat().st_mtime)

def main():
    parser = argparse.ArgumentParser(description="Run Vidur simulation with extracted trace data")
    parser.add_argument("--trace-file", "-t",
                       help="Trace CSV file (if not specified, will use latest)")
    parser.add_argument("--model", default="/share_data/llm_weights/Meta-Llama-3.1-8B",
                       help="Model path")
    parser.add_argument("--device", default="a100",
                       help="Device type")
    parser.add_argument("--scheduler", default="sarathi",
                       choices=["sarathi", "vllm", "orca"],
                       help="Scheduler type")
    parser.add_argument("--batch-size-cap", type=int, default=512,
                       help="Batch size cap")
    parser.add_argument("--chunk-size", type=int, default=512,
                       help="Chunk size")
    parser.add_argument("--max-tokens", type=int, default=16384,
                       help="Maximum tokens")
    parser.add_argument("--tensor-parallel-size", type=int, default=1,
                       help="Tensor parallel size")
    parser.add_argument("--num-replicas", type=int, default=1,
                       help="Number of replicas")
    parser.add_argument("--num-pipeline-stages", type=int, default=1,
                       help="Number of pipeline stages")
    
    args = parser.parse_args()
    
    # 确定 trace 文件
    if args.trace_file:
        trace_file = Path(args.trace_file)
        if not trace_file.exists():
            print_error(f"Trace file not found: {trace_file}")
            return 1
    else:
        trace_file = find_latest_trace_file()
        if trace_file is None:
            print_error("No trace file found. Please run step2_extract_data.py first.")
            return 1
        print_info(f"Using latest trace file: {trace_file}")
    
    try:
        # 创建仿真器
        simulator = VidurSimulator(trace_file, args.model)
        
        # 运行仿真
        output_dir = simulator.run_simulation(
            device=args.device,
            num_replicas=args.num_replicas,
            tensor_parallel_size=args.tensor_parallel_size,
            num_pipeline_stages=args.num_pipeline_stages,
            scheduler_type=args.scheduler,
            batch_size_cap=args.batch_size_cap,
            chunk_size=args.chunk_size,
            max_tokens=args.max_tokens
        )
        
        if output_dir:
            print_info("Vidur simulation completed successfully!")
            print_info(f"Results directory: {output_dir}")
            print_info("Next step: Run 'python step4_compare_results.py' to compare results")
        else:
            print_warning("Simulation completed but output directory not found")
        
        return 0
        
    except Exception as e:
        print_error(f"Vidur simulation failed: {e}")
        return 1

if __name__ == "__main__":
    exit(main())
