#!/usr/bin/env python3
"""
vLLM 诊断脚本
用于检查 vLLM 环境和配置问题
"""

import subprocess
import sys
import time
import requests
import psutil
import os
from pathlib import Path

# Colors for output
RED = '\033[0;31m'
GREEN = '\033[0;32m'
YELLOW = '\033[1;33m'
BLUE = '\033[0;34m'
NC = '\033[0m'

def print_info(msg):
    print(f"{GREEN}[INFO]{NC} {msg}")

def print_warning(msg):
    print(f"{YELLOW}[WARNING]{NC} {msg}")

def print_error(msg):
    print(f"{RED}[ERROR]{NC} {msg}")

def print_step(msg):
    print(f"{BLUE}[STEP]{NC} {msg}")

def check_system_resources():
    """检查系统资源"""
    print_step("Checking system resources...")
    
    # 检查内存
    memory = psutil.virtual_memory()
    print_info(f"Total RAM: {memory.total / (1024**3):.1f} GB")
    print_info(f"Available RAM: {memory.available / (1024**3):.1f} GB")
    print_info(f"RAM usage: {memory.percent:.1f}%")
    
    if memory.available < 8 * (1024**3):  # 8GB
        print_warning("Available RAM < 8GB, may cause issues with large models")
    
    # 检查磁盘空间
    disk = psutil.disk_usage('/')
    print_info(f"Disk space: {disk.free / (1024**3):.1f} GB free / {disk.total / (1024**3):.1f} GB total")
    
    if disk.free < 50 * (1024**3):  # 50GB
        print_warning("Free disk space < 50GB, may not be enough for model caching")

def check_gpu():
    """检查 GPU 状态"""
    print_step("Checking GPU status...")
    
    try:
        result = subprocess.run(["nvidia-smi"], capture_output=True, text=True)
        if result.returncode == 0:
            print_info("GPU detected:")
            # 解析 nvidia-smi 输出
            lines = result.stdout.split('\n')
            for line in lines:
                if 'GeForce' in line or 'Tesla' in line or 'A100' in line or 'V100' in line:
                    print(f"  {line.strip()}")
        else:
            print_error("nvidia-smi failed - no GPU detected or driver issues")
            return False
    except FileNotFoundError:
        print_error("nvidia-smi not found - NVIDIA drivers not installed")
        return False
    
    # 检查 CUDA
    try:
        import torch
        if torch.cuda.is_available():
            print_info(f"CUDA available: {torch.cuda.device_count()} devices")
            for i in range(torch.cuda.device_count()):
                props = torch.cuda.get_device_properties(i)
                print_info(f"  Device {i}: {props.name}, {props.total_memory / (1024**3):.1f} GB")
        else:
            print_error("CUDA not available in PyTorch")
            return False
    except ImportError:
        print_error("PyTorch not installed")
        return False
    
    return True

def check_vllm_installation():
    """检查 vLLM 安装"""
    print_step("Checking vLLM installation...")
    
    try:
        import vllm
        print_info(f"vLLM version: {vllm.__version__}")
    except ImportError:
        print_error("vLLM not installed")
        return False
    
    # 检查 vLLM 命令
    try:
        result = subprocess.run(
            ["python", "-m", "vllm.entrypoints.openai.api_server", "--help"],
            capture_output=True, text=True, timeout=10
        )
        if result.returncode == 0:
            print_info("vLLM API server command available")
        else:
            print_error("vLLM API server command failed")
            return False
    except Exception as e:
        print_error(f"Failed to check vLLM command: {e}")
        return False
    
    return True

def check_port_availability(port=8000):
    """检查端口可用性"""
    print_step(f"Checking port {port} availability...")
    
    try:
        result = subprocess.run(
            ["netstat", "-tulpn"], capture_output=True, text=True
        )
        if f":{port}" in result.stdout:
            print_warning(f"Port {port} is already in use")
            print("Processes using the port:")
            for line in result.stdout.split('\n'):
                if f":{port}" in line:
                    print(f"  {line}")
            return False
        else:
            print_info(f"Port {port} is available")
            return True
    except:
        print_warning("Could not check port status")
        return True

def test_model_access(model_name="meta-llama/Meta-Llama-3-8B"):
    """测试模型访问"""
    print_step(f"Testing model access: {model_name}")
    
    # 检查 Hugging Face 缓存
    cache_dir = Path.home() / ".cache" / "huggingface"
    if cache_dir.exists():
        print_info(f"HuggingFace cache directory exists: {cache_dir}")
        
        # 查找模型文件
        model_cache = cache_dir / "hub"
        if model_cache.exists():
            model_dirs = [d for d in model_cache.iterdir() if model_name.replace('/', '--') in d.name]
            if model_dirs:
                print_info(f"Model cache found: {len(model_dirs)} directories")
            else:
                print_warning("Model not found in cache - will need to download")
    else:
        print_warning("HuggingFace cache directory not found")
    
    # 检查网络连接
    try:
        response = requests.get("https://huggingface.co", timeout=10)
        if response.status_code == 200:
            print_info("HuggingFace Hub accessible")
        else:
            print_warning("HuggingFace Hub connection issues")
    except:
        print_error("Cannot connect to HuggingFace Hub - check internet connection")

def test_vllm_server_startup(model_name="meta-llama/Meta-Llama-3-8B", timeout=300):
    """测试 vLLM 服务器启动"""
    print_step("Testing vLLM server startup...")
    
    port = 8001  # 使用不同端口避免冲突
    
    cmd = [
        "python", "-m", "vllm.entrypoints.openai.api_server",
        "--model", model_name,
        "--tensor-parallel-size", "1",
        "--max-model-len", "4096",  # 较小的长度
        "--port", str(port),
        "--disable-log-requests"
    ]
    
    print_info(f"Starting server with command: {' '.join(cmd)}")
    print_info("This may take several minutes for first-time model loading...")
    
    log_file = "vllm_test.log"
    
    try:
        with open(log_file, 'w') as f:
            process = subprocess.Popen(
                cmd,
                stdout=f,
                stderr=subprocess.STDOUT,
                text=True
            )
        
        print_info(f"Server logs: {log_file}")
        
        # 等待服务器启动
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                response = requests.get(f"http://localhost:{port}/health", timeout=5)
                if response.status_code == 200:
                    print_info("✓ Server started successfully!")
                    
                    # 测试模型列表
                    try:
                        models_response = requests.get(f"http://localhost:{port}/v1/models", timeout=10)
                        if models_response.status_code == 200:
                            print_info("✓ Model API accessible")
                        else:
                            print_warning("Model API not accessible")
                    except:
                        print_warning("Could not test model API")
                    
                    # 停止服务器
                    process.terminate()
                    process.wait(timeout=10)
                    print_info("Server stopped")
                    return True
                    
            except:
                pass
            
            # 检查进程是否还在运行
            if process.poll() is not None:
                print_error("Server process exited unexpectedly")
                break
            
            print(".", end="", flush=True)
            time.sleep(10)
        
        print()
        print_error("Server startup timed out")
        
        # 停止进程
        try:
            process.terminate()
            process.wait(timeout=10)
        except:
            process.kill()
        
        # 显示日志
        print_error("Last few lines of server log:")
        try:
            with open(log_file, 'r') as f:
                lines = f.readlines()
                for line in lines[-20:]:
                    print(f"  {line.rstrip()}")
        except:
            pass
        
        return False
        
    except Exception as e:
        print_error(f"Failed to start server: {e}")
        return False

def main():
    print_step("vLLM Environment Diagnosis")
    print("=" * 50)
    
    all_checks_passed = True
    
    # 系统资源检查
    check_system_resources()
    print()
    
    # GPU 检查
    if not check_gpu():
        all_checks_passed = False
    print()
    
    # vLLM 安装检查
    if not check_vllm_installation():
        all_checks_passed = False
    print()
    
    # 端口检查
    if not check_port_availability():
        all_checks_passed = False
    print()
    
    # 模型访问检查
    test_model_access()
    print()
    
    if all_checks_passed:
        print_step("All basic checks passed. Testing server startup...")
        if test_vllm_server_startup():
            print_info("✓ All tests passed! vLLM should work correctly.")
        else:
            print_error("✗ Server startup test failed. Check the logs above.")
    else:
        print_error("✗ Some basic checks failed. Please fix the issues above before proceeding.")
    
    print("\n" + "=" * 50)
    print_step("Diagnosis complete")

if __name__ == "__main__":
    main()
