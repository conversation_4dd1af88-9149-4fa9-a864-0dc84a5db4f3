#!/usr/bin/env python3
"""
检查本地模型是否可用的快速验证脚本
"""

import os
import sys
from pathlib import Path

# Colors for output
RED = '\033[0;31m'
GREEN = '\033[0;32m'
YELLOW = '\033[1;33m'
BLUE = '\033[0;34m'
NC = '\033[0m'

def print_info(msg):
    print(f"{GREEN}[INFO]{NC} {msg}")

def print_warning(msg):
    print(f"{YELLOW}[WARNING]{NC} {msg}")

def print_error(msg):
    print(f"{RED}[ERROR]{NC} {msg}")

def print_step(msg):
    print(f"{BLUE}[STEP]{NC} {msg}")

def check_local_model(model_path="/share_data/llm_weights/Meta-Llama-3.1-8B"):
    """检查本地模型文件"""
    print_step(f"Checking local model: {model_path}")
    
    model_dir = Path(model_path)
    
    if not model_dir.exists():
        print_error(f"Model directory does not exist: {model_path}")
        return False
    
    if not model_dir.is_dir():
        print_error(f"Model path is not a directory: {model_path}")
        return False
    
    print_info(f"✓ Model directory exists: {model_dir}")
    
    # 检查必需的文件
    required_files = {
        "config.json": "Model configuration",
        "tokenizer.json": "Tokenizer",
        "tokenizer_config.json": "Tokenizer configuration",
        "generation_config.json": "Generation configuration (optional)"
    }
    
    found_files = []
    missing_files = []
    
    for filename, description in required_files.items():
        file_path = model_dir / filename
        if file_path.exists():
            print_info(f"  ✓ {filename} - {description}")
            found_files.append(filename)
        else:
            if filename == "generation_config.json":
                print_warning(f"  ? {filename} - {description} (optional, missing)")
            else:
                print_error(f"  ✗ {filename} - {description} (required, missing)")
                missing_files.append(filename)
    
    # 检查模型权重文件
    print_step("Checking model weight files...")
    
    safetensors_files = list(model_dir.glob("*.safetensors"))
    bin_files = list(model_dir.glob("*.bin"))
    
    weight_files = safetensors_files + bin_files
    
    if weight_files:
        print_info(f"✓ Found {len(weight_files)} weight files:")
        total_size = 0
        for weight_file in sorted(weight_files):
            size_gb = weight_file.stat().st_size / (1024**3)
            total_size += size_gb
            print_info(f"    {weight_file.name} ({size_gb:.2f} GB)")
        
        print_info(f"✓ Total model size: {total_size:.2f} GB")
        
        # 检查是否有合理的大小（8B模型大约15-20GB）
        if total_size < 10:
            print_warning("Model size seems small for an 8B parameter model")
        elif total_size > 50:
            print_warning("Model size seems large for an 8B parameter model")
        else:
            print_info("Model size looks reasonable for an 8B parameter model")
    else:
        print_error("✗ No weight files found (.safetensors or .bin)")
        missing_files.append("weight files")
    
    # 检查其他可能有用的文件
    print_step("Checking additional files...")
    
    optional_files = [
        "tokenizer_config.json",
        "special_tokens_map.json", 
        "vocab.json",
        "merges.txt",
        "added_tokens.json"
    ]
    
    for filename in optional_files:
        file_path = model_dir / filename
        if file_path.exists():
            print_info(f"  ✓ {filename}")
    
    # 检查目录权限
    print_step("Checking directory permissions...")
    
    if os.access(model_dir, os.R_OK):
        print_info("✓ Directory is readable")
    else:
        print_error("✗ Directory is not readable")
        return False
    
    # 最终结果
    if missing_files:
        print_error(f"✗ Model validation failed. Missing files: {', '.join(missing_files)}")
        return False
    else:
        print_info("✓ Model validation passed! All required files are present.")
        return True

def main():
    model_path = "/share_data/llm_weights/Meta-Llama-3.1-8B"
    
    if len(sys.argv) > 1:
        model_path = sys.argv[1]
    
    print_step("Local Model Validation")
    print("=" * 50)
    
    success = check_local_model(model_path)
    
    print("\n" + "=" * 50)
    
    if success:
        print_info("✓ Model is ready for use with vLLM!")
        print_info("You can now run the validation pipeline:")
        print_info("  python run_full_validation.py")
    else:
        print_error("✗ Model validation failed!")
        print_error("Please check the model files and try again.")
        print_error("Make sure the model is properly downloaded and extracted.")
    
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())
