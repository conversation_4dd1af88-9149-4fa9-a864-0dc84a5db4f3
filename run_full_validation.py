#!/usr/bin/env python3
"""
Full Validation Pipeline
This script runs the complete validation pipeline to verify <PERSON><PERSON><PERSON>'s accuracy claims.
"""

import subprocess
import argparse
import sys
import time
from pathlib import Path

# Colors for output
RED = '\033[0;31m'
GREEN = '\033[0;32m'
YELLOW = '\033[1;33m'
BLUE = '\033[0;34m'
CYAN = '\033[0;36m'
NC = '\033[0m'

def print_info(msg):
    print(f"{GREEN}[INFO]{NC} {msg}")

def print_warning(msg):
    print(f"{YELLOW}[WARNING]{NC} {msg}")

def print_error(msg):
    print(f"{RED}[ERROR]{NC} {msg}")

def print_step(msg):
    print(f"{BLUE}[STEP]{NC} {msg}")

def print_header(msg):
    print(f"\n{CYAN}{'='*60}{NC}")
    print(f"{<PERSON><PERSON><PERSON>}{msg.center(60)}{NC}")
    print(f"{CYAN}{'='*60}{NC}\n")

class ValidationPipeline:
    def __init__(self, model="/share_data/llm_weights/Meta-Llama-3.1-8B",
                 dataset="sharegpt", num_prompts=512, request_rate=6.45):
        self.model = model
        self.dataset = dataset
        self.num_prompts = num_prompts
        self.request_rate = request_rate
        self.start_time = time.time()
        
        # 创建结果目录
        self.results_dir = Path("validation_results")
        self.results_dir.mkdir(exist_ok=True)
    
    def run_step(self, step_name, script_name, args=None):
        """运行单个步骤"""
        print_header(f"Step: {step_name}")
        
        cmd = ["python", script_name]
        if args:
            cmd.extend(args)
        
        print_info(f"Running: {' '.join(cmd)}")
        
        try:
            result = subprocess.run(
                cmd,
                capture_output=False,  # 显示实时输出
                text=True
            )
            
            if result.returncode == 0:
                print_info(f"✓ {step_name} completed successfully!")
                return True
            else:
                print_error(f"✗ {step_name} failed with return code {result.returncode}")
                return False
                
        except Exception as e:
            print_error(f"✗ {step_name} failed with exception: {e}")
            return False
    
    def run_full_pipeline(self, skip_vllm=False, skip_vidur=False):
        """运行完整的验证流程"""
        print_header("Vidur Accuracy Validation Pipeline")
        print_info(f"Configuration:")
        print_info(f"  Model: {self.model}")
        print_info(f"  Dataset: {self.dataset}")
        print_info(f"  Number of prompts: {self.num_prompts}")
        print_info(f"  Request rate: {self.request_rate} QPS")
        
        success_count = 0
        total_steps = 4
        
        # Step 1: Run vLLM benchmark
        if not skip_vllm:
            step1_args = [
                "--model", self.model,
                "--dataset", self.dataset,
                "--num-prompts", str(self.num_prompts),
                "--request-rate", str(self.request_rate)
            ]
            
            if self.run_step("vLLM Benchmark", "step1_run_vllm_benchmark.py", step1_args):
                success_count += 1
            else:
                print_error("Pipeline failed at Step 1")
                return False
        else:
            print_warning("Skipping vLLM benchmark (--skip-vllm)")
            success_count += 1
        
        # Step 2: Extract data
        if self.run_step("Data Extraction", "step2_extract_data.py"):
            success_count += 1
        else:
            print_error("Pipeline failed at Step 2")
            return False
        
        # Step 3: Run Vidur simulation
        if not skip_vidur:
            step3_args = [
                "--model", self.model
            ]
            
            if self.run_step("Vidur Simulation", "step3_run_vidur_simulation.py", step3_args):
                success_count += 1
            else:
                print_error("Pipeline failed at Step 3")
                return False
        else:
            print_warning("Skipping Vidur simulation (--skip-vidur)")
            success_count += 1
        
        # Step 4: Compare results
        if self.run_step("Results Comparison", "step4_compare_results.py"):
            success_count += 1
        else:
            print_error("Pipeline failed at Step 4")
            return False
        
        # Final summary
        self.print_final_summary(success_count, total_steps)
        return success_count == total_steps
    
    def print_final_summary(self, success_count, total_steps):
        """打印最终汇总"""
        duration = time.time() - self.start_time
        
        print_header("Validation Pipeline Summary")
        
        if success_count == total_steps:
            print_info(f"✓ All {total_steps} steps completed successfully!")
            print_info(f"✓ Total execution time: {duration:.1f} seconds")
            print_info(f"✓ Results available in:")
            print_info(f"    - vLLM benchmark: vllm_benchmark_results/")
            print_info(f"    - Extracted data: vidur_input_data/")
            print_info(f"    - Vidur simulation: simulator_output/")
            print_info(f"    - Comparison results: comparison_results/")
            
            print_header("Next Steps")
            print_info("1. Check comparison_results/comparison_report.txt for detailed analysis")
            print_info("2. View comparison_results/performance_comparison.png for visualizations")
            print_info("3. Examine comparison_results/detailed_comparison.csv for raw data")
            
        else:
            print_error(f"✗ Pipeline incomplete: {success_count}/{total_steps} steps succeeded")
            print_error(f"✗ Execution time: {duration:.1f} seconds")
            print_error("Please check the error messages above and retry failed steps")

def check_dependencies():
    """检查必要的依赖"""
    print_step("Checking dependencies...")
    
    required_packages = ['pandas', 'numpy', 'matplotlib', 'seaborn']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print_error(f"Missing required packages: {', '.join(missing_packages)}")
        print_error("Please install them using: pip install " + " ".join(missing_packages))
        return False
    
    # 检查 Vidur 是否可用
    try:
        result = subprocess.run(
            ["python", "-m", "vidur.main", "--help"],
            capture_output=True,
            text=True,
            timeout=10
        )
        if result.returncode != 0:
            print_error("Vidur module not found or not working properly")
            print_error("Please ensure Vidur is properly installed")
            return False
    except Exception as e:
        print_error(f"Failed to check Vidur: {e}")
        return False
    
    print_info("✓ All dependencies are available")
    return True

def main():
    parser = argparse.ArgumentParser(
        description="Run complete Vidur accuracy validation pipeline",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Run full pipeline with default settings
  python run_full_validation.py
  
  # Use different model and dataset
  python run_full_validation.py --model meta-llama/Meta-Llama-3-70B --dataset sonnet
  
  # Skip vLLM benchmark (use existing results)
  python run_full_validation.py --skip-vllm
  
  # Run with fewer prompts for quick testing
  python run_full_validation.py --num-prompts 100
        """
    )
    
    parser.add_argument("--model", default="/share_data/llm_weights/Meta-Llama-3.1-8B",
                       help="Model path to test")
    parser.add_argument("--dataset", default="sharegpt",
                       choices=["sharegpt", "sonnet", "random", "burstgpt"],
                       help="Dataset for benchmarking")
    parser.add_argument("--num-prompts", type=int, default=512,
                       help="Number of prompts to test")
    parser.add_argument("--request-rate", type=float, default=6.45,
                       help="Request rate (QPS)")
    parser.add_argument("--skip-vllm", action="store_true",
                       help="Skip vLLM benchmark (use existing results)")
    parser.add_argument("--skip-vidur", action="store_true",
                       help="Skip Vidur simulation (use existing results)")
    parser.add_argument("--check-deps", action="store_true",
                       help="Only check dependencies and exit")
    
    args = parser.parse_args()
    
    # 检查依赖
    if not check_dependencies():
        return 1
    
    if args.check_deps:
        print_info("Dependency check completed successfully!")
        return 0
    
    # 创建并运行验证流程
    pipeline = ValidationPipeline(
        model=args.model,
        dataset=args.dataset,
        num_prompts=args.num_prompts,
        request_rate=args.request_rate
    )
    
    try:
        success = pipeline.run_full_pipeline(
            skip_vllm=args.skip_vllm,
            skip_vidur=args.skip_vidur
        )
        
        return 0 if success else 1
        
    except KeyboardInterrupt:
        print_warning("\nPipeline interrupted by user")
        return 1
    except Exception as e:
        print_error(f"Pipeline failed with unexpected error: {e}")
        return 1

if __name__ == "__main__":
    exit(main())
